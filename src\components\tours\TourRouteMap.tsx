import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Route, Navigation, Clock } from 'lucide-react';
import LeafletMap from '@/components/maps/LeafletMap';
import { Tour } from '@/types/firebase';

interface TourRouteMapProps {
  tour: Tour;
}

const TourRouteMap: React.FC<TourRouteMapProps> = ({ tour }) => {
  // Create route points from tour data
  const createRoutePoints = () => {
    const points = [];
    
    // Add start point if available
    if (tour.routeMap?.startPoint) {
      points.push({
        ...tour.routeMap.startPoint,
        type: 'start' as const,
        description: 'Tour starting point'
      });
    }
    
    // Add waypoints if available
    if (tour.routeMap?.waypoints) {
      tour.routeMap.waypoints
        .sort((a, b) => a.order - b.order)
        .forEach(waypoint => {
          points.push({
            ...waypoint,
            type: 'waypoint' as const
          });
        });
    }
    
    // Add end point if available
    if (tour.routeMap?.endPoint) {
      points.push({
        ...tour.routeMap.endPoint,
        type: 'end' as const,
        description: 'Tour ending point'
      });
    }
    
    // If no route data is available, create default points from destinations
    if (points.length === 0 && tour.destinations) {
      const defaultDestinations = [
        { name: 'Serengeti National Park', lat: -2.153389, lng: 34.6857 },
        { name: 'Ngorongoro Crater', lat: -3.2175, lng: 35.5 },
        { name: 'Tarangire National Park', lat: -3.9, lng: 35.85 },
        { name: 'Lake Manyara', lat: -3.48, lng: 35.83 }
      ];
      
      tour.destinations.forEach((destName, index) => {
        const defaultDest = defaultDestinations.find(d => 
          d.name.toLowerCase().includes(destName.toLowerCase()) ||
          destName.toLowerCase().includes(d.name.toLowerCase())
        ) || defaultDestinations[index % defaultDestinations.length];
        
        points.push({
          lat: defaultDest.lat,
          lng: defaultDest.lng,
          name: destName,
          type: index === 0 ? 'start' : index === tour.destinations.length - 1 ? 'end' : 'waypoint',
          description: `Visit ${destName}`
        });
      });
    }
    
    return points;
  };

  const routePoints = createRoutePoints();
  
  // Convert route points to destinations format for the map
  const mapDestinations = routePoints.map((point, index) => ({
    id: `point-${index}`,
    name: point.name,
    lat: point.lat,
    lng: point.lng,
    description: point.description || `${point.type} point`,
    image: 'photo-1472396961693-142e6e269027' // Default safari image
  }));

  const getPointTypeIcon = (type: string) => {
    switch (type) {
      case 'start':
        return '🚀';
      case 'end':
        return '🏁';
      case 'waypoint':
        return '📍';
      default:
        return '📍';
    }
  };

  const getPointTypeColor = (type: string) => {
    switch (type) {
      case 'start':
        return 'bg-green-100 text-green-800';
      case 'end':
        return 'bg-red-100 text-red-800';
      case 'waypoint':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Route Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Route className="mr-2 h-5 w-5 text-orange-600" />
            Safari Route Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <MapPin className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="font-semibold">{routePoints.length}</div>
              <div className="text-sm text-gray-600">Total Stops</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="font-semibold">{tour.duration}</div>
              <div className="text-sm text-gray-600">Duration</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Navigation className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="font-semibold">Tanzania</div>
              <div className="text-sm text-gray-600">Region</div>
            </div>
          </div>

          {/* Route Points List */}
          <div className="space-y-3">
            <h4 className="font-semibold text-lg mb-3">Route Itinerary</h4>
            {routePoints.map((point, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 bg-white border rounded-lg">
                <div className="text-2xl">{getPointTypeIcon(point.type)}</div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h5 className="font-medium">{point.name}</h5>
                    <Badge className={getPointTypeColor(point.type)}>
                      {point.type}
                    </Badge>
                  </div>
                  {point.description && (
                    <p className="text-sm text-gray-600 mt-1">{point.description}</p>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  Stop {index + 1}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Interactive Map */}
      <LeafletMap
        destinations={mapDestinations}
        showRoutes={true}
        height="500px"
        onDestinationClick={(destination) => {
          console.log('Clicked destination:', destination);
        }}
      />

      {/* Route Information */}
      <Card>
        <CardHeader>
          <CardTitle>Route Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">What to Expect</h4>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Professional guide throughout the journey
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Comfortable 4WD safari vehicles
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Strategic stops for wildlife viewing
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Photo opportunities at scenic viewpoints
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Route Highlights</h4>
              <ul className="space-y-2 text-sm text-gray-700">
                {tour.destinations?.slice(0, 4).map((destination, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-orange-600 mr-2">•</span>
                    {destination}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TourRouteMap;
