
import React from 'react';
import { Shield, Star, Users, MapPin, Heart, Camera } from 'lucide-react';

const ValuePropositions = () => {
  const values = [
    {
      icon: Shield,
      title: 'Expert Local Guides',
      description: 'Professional guides with deep knowledge of wildlife behavior and local culture',
      color: 'text-blue-600'
    },
    {
      icon: Star,
      title: '5-Star Experiences',
      description: 'Luxury accommodations and premium safari vehicles for maximum comfort',
      color: 'text-yellow-600'
    },
    {
      icon: Users,
      title: 'Small Group Tours',
      description: 'Intimate group sizes for personalized attention and better wildlife viewing',
      color: 'text-green-600'
    },
    {
      icon: MapPin,
      title: 'Prime Locations',
      description: 'Access to the best national parks and conservancies in Tanzania',
      color: 'text-red-600'
    },
    {
      icon: Heart,
      title: 'Conservation Focus',
      description: 'Supporting local communities and wildlife conservation efforts',
      color: 'text-purple-600'
    },
    {
      icon: Camera,
      title: 'Photography Tours',
      description: 'Specialized tours for wildlife photography with expert guidance',
      color: 'text-orange-600'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose SafariSole Tours?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            With over 15 years of experience, we provide unforgettable safari adventures 
            that combine luxury, authenticity, and conservation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <div
                key={index}
                className="group bg-white p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gray-50 ${value.color} mb-4 group-hover:scale-110 transition-transform`}>
                  <IconComponent className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Statistics Section */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-sm">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">
                10,000+
              </div>
              <div className="text-gray-600">Happy Travelers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">
                15+
              </div>
              <div className="text-gray-600">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">
                98%
              </div>
              <div className="text-gray-600">Satisfaction Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">
                50+
              </div>
              <div className="text-gray-600">Tour Packages</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ValuePropositions;
