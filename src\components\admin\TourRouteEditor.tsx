import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Plus, 
  Trash2, 
  Save, 
  Navigation, 
  Route,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import LeafletMap from '@/components/maps/LeafletMap';
import { Tour } from '@/types/firebase';

interface TourRouteEditorProps {
  tour: Tour;
  onSave: (routeData: Tour['routeMap']) => void;
}

const TourRouteEditor: React.FC<TourRouteEditorProps> = ({ tour, onSave }) => {
  const [startPoint, setStartPoint] = useState(tour.routeMap?.startPoint || {
    lat: -3.3731, lng: 35.7419, name: '<PERSON><PERSON><PERSON>'
  });
  
  const [endPoint, setEndPoint] = useState(tour.routeMap?.endPoint || {
    lat: -3.3731, lng: 35.7419, name: 'Arusha'
  });
  
  const [waypoints, setWaypoints] = useState(tour.routeMap?.waypoints || []);

  const addWaypoint = () => {
    const newWaypoint = {
      lat: -2.153389,
      lng: 34.6857,
      name: 'New Waypoint',
      description: '',
      order: waypoints.length + 1
    };
    setWaypoints([...waypoints, newWaypoint]);
  };

  const removeWaypoint = (index: number) => {
    const updatedWaypoints = waypoints.filter((_, i) => i !== index);
    // Reorder remaining waypoints
    const reorderedWaypoints = updatedWaypoints.map((wp, i) => ({
      ...wp,
      order: i + 1
    }));
    setWaypoints(reorderedWaypoints);
  };

  const updateWaypoint = (index: number, field: string, value: any) => {
    const updatedWaypoints = waypoints.map((wp, i) => 
      i === index ? { ...wp, [field]: value } : wp
    );
    setWaypoints(updatedWaypoints);
  };

  const moveWaypoint = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === waypoints.length - 1)
    ) {
      return;
    }

    const newWaypoints = [...waypoints];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    // Swap waypoints
    [newWaypoints[index], newWaypoints[targetIndex]] = 
    [newWaypoints[targetIndex], newWaypoints[index]];
    
    // Update order numbers
    newWaypoints.forEach((wp, i) => {
      wp.order = i + 1;
    });
    
    setWaypoints(newWaypoints);
  };

  const handleSave = () => {
    const routeData = {
      startPoint,
      endPoint,
      waypoints: waypoints.map((wp, index) => ({ ...wp, order: index + 1 }))
    };
    onSave(routeData);
  };

  // Create map destinations for preview
  const mapDestinations = [
    {
      id: 'start',
      name: startPoint.name,
      lat: startPoint.lat,
      lng: startPoint.lng,
      description: 'Tour starting point'
    },
    ...waypoints.map((wp, index) => ({
      id: `waypoint-${index}`,
      name: wp.name,
      lat: wp.lat,
      lng: wp.lng,
      description: wp.description || `Waypoint ${index + 1}`
    })),
    {
      id: 'end',
      name: endPoint.name,
      lat: endPoint.lat,
      lng: endPoint.lng,
      description: 'Tour ending point'
    }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Route className="mr-2 h-5 w-5" />
            Tour Route Editor
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Start Point */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="start-name">Start Point Name</Label>
              <Input
                id="start-name"
                value={startPoint.name}
                onChange={(e) => setStartPoint({ ...startPoint, name: e.target.value })}
                placeholder="e.g., Arusha Airport"
              />
            </div>
            <div>
              <Label htmlFor="start-lat">Latitude</Label>
              <Input
                id="start-lat"
                type="number"
                step="0.000001"
                value={startPoint.lat}
                onChange={(e) => setStartPoint({ ...startPoint, lat: parseFloat(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="start-lng">Longitude</Label>
              <Input
                id="start-lng"
                type="number"
                step="0.000001"
                value={startPoint.lng}
                onChange={(e) => setStartPoint({ ...startPoint, lng: parseFloat(e.target.value) })}
              />
            </div>
          </div>

          {/* Waypoints */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold">Waypoints</h4>
              <Button onClick={addWaypoint} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Waypoint
              </Button>
            </div>
            
            <div className="space-y-4">
              {waypoints.map((waypoint, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <Badge variant="outline">Waypoint {index + 1}</Badge>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => moveWaypoint(index, 'up')}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => moveWaypoint(index, 'down')}
                          disabled={index === waypoints.length - 1}
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeWaypoint(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                      <Input
                        placeholder="Waypoint name"
                        value={waypoint.name}
                        onChange={(e) => updateWaypoint(index, 'name', e.target.value)}
                      />
                      <Input
                        type="number"
                        step="0.000001"
                        placeholder="Latitude"
                        value={waypoint.lat}
                        onChange={(e) => updateWaypoint(index, 'lat', parseFloat(e.target.value))}
                      />
                      <Input
                        type="number"
                        step="0.000001"
                        placeholder="Longitude"
                        value={waypoint.lng}
                        onChange={(e) => updateWaypoint(index, 'lng', parseFloat(e.target.value))}
                      />
                    </div>
                    
                    <Textarea
                      placeholder="Waypoint description (optional)"
                      value={waypoint.description || ''}
                      onChange={(e) => updateWaypoint(index, 'description', e.target.value)}
                      rows={2}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* End Point */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="end-name">End Point Name</Label>
              <Input
                id="end-name"
                value={endPoint.name}
                onChange={(e) => setEndPoint({ ...endPoint, name: e.target.value })}
                placeholder="e.g., Arusha Airport"
              />
            </div>
            <div>
              <Label htmlFor="end-lat">Latitude</Label>
              <Input
                id="end-lat"
                type="number"
                step="0.000001"
                value={endPoint.lat}
                onChange={(e) => setEndPoint({ ...endPoint, lat: parseFloat(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="end-lng">Longitude</Label>
              <Input
                id="end-lng"
                type="number"
                step="0.000001"
                value={endPoint.lng}
                onChange={(e) => setEndPoint({ ...endPoint, lng: parseFloat(e.target.value) })}
              />
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
              <Save className="mr-2 h-4 w-4" />
              Save Route
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Route Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5" />
            Route Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LeafletMap
            destinations={mapDestinations}
            showRoutes={true}
            height="400px"
          />
          <div className="mt-4 text-sm text-gray-600">
            <div className="flex items-center justify-between">
              <span>Route includes {mapDestinations.length} points</span>
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                  Start
                </span>
                <span className="flex items-center">
                  <span className="w-3 h-3 bg-blue-500 rounded-full mr-1"></span>
                  Waypoints
                </span>
                <span className="flex items-center">
                  <span className="w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                  End
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TourRouteEditor;
