import { DestinationService } from '@/services/destinationService';

// Test utility for Quick Search Form functionality
export const testQuickSearchForm = () => {
  console.log('🔍 Testing Quick Search Form...');

  // Test 1: Check if form elements exist
  console.log('1️⃣ Checking form elements...');
  const form = document.querySelector('form');
  const destinationInput = document.querySelector('input[list="destinations"]') as HTMLInputElement;
  const dateInput = document.querySelector('input[type="date"]') as HTMLInputElement;
  const travelersSelect = document.querySelector('select') as HTMLSelectElement;
  const searchButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;

  if (form && destinationInput && dateInput && travelersSelect && searchButton) {
    console.log('✅ All form elements found');
  } else {
    console.log('❌ Some form elements missing');
    return false;
  }

  // Test 2: Check datalist for destinations
  console.log('2️⃣ Checking destination suggestions...');
  const datalist = document.querySelector('#destinations');
  if (datalist && datalist.children.length > 0) {
    console.log('✅ Destination suggestions available:', datalist.children.length);
    console.log('📍 Sample destinations:', Array.from(datalist.children).slice(0, 3).map(option => (option as HTMLOptionElement).value));
  } else {
    console.log('❌ No destination suggestions found');
  }

  // Test 3: Check popular tags
  console.log('3️⃣ Checking popular tags...');
  const popularTags = document.querySelectorAll('button[type="button"]');
  if (popularTags.length > 0) {
    console.log('✅ Popular tags found:', popularTags.length);
    console.log('🏷️ Tag labels:', Array.from(popularTags).map(tag => tag.textContent));
  } else {
    console.log('❌ No popular tags found');
  }

  // Test 4: Simulate form interaction
  console.log('4️⃣ Testing form interaction...');
  try {
    // Fill destination with a real destination from the datalist
    const firstDestination = datalist?.children[0] as HTMLOptionElement;
    const destinationValue = firstDestination?.value || 'Serengeti National Park';

    destinationInput.value = destinationValue;
    destinationInput.dispatchEvent(new Event('change', { bubbles: true }));

    // Set date (tomorrow)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.value = tomorrow.toISOString().split('T')[0];
    dateInput.dispatchEvent(new Event('change', { bubbles: true }));

    // Set travelers
    travelersSelect.value = '4';
    travelersSelect.dispatchEvent(new Event('change', { bubbles: true }));

    console.log('✅ Form filled successfully');
    console.log('📝 Form values:', {
      destination: destinationInput.value,
      date: dateInput.value,
      travelers: travelersSelect.value
    });

    return true;
  } catch (error) {
    console.log('❌ Form interaction failed:', error);
    return false;
  }
};

// Test function for popular tag clicks
export const testPopularTags = () => {
  console.log('🏷️ Testing popular tags...');
  
  const popularTags = document.querySelectorAll('button[type="button"]');
  
  if (popularTags.length === 0) {
    console.log('❌ No popular tags found');
    return false;
  }
  
  console.log('✅ Found', popularTags.length, 'popular tags');
  
  // Test clicking the first tag
  try {
    const firstTag = popularTags[0] as HTMLButtonElement;
    console.log('🖱️ Testing click on:', firstTag.textContent);
    
    // Simulate click
    firstTag.click();
    
    console.log('✅ Tag click successful');
    return true;
  } catch (error) {
    console.log('❌ Tag click failed:', error);
    return false;
  }
};

// Test URL parameter handling
export const testURLParameters = () => {
  console.log('🔗 Testing URL parameters...');
  
  const testParams = [
    { destination: 'Serengeti National Park', search: 'Serengeti National Park' },
    { destination: 'Ngorongoro Crater', date: '2024-07-15', travelers: '4' },
    { search: 'safari', destination: 'Tarangire National Park' }
  ];
  
  testParams.forEach((params, index) => {
    console.log(`${index + 1}️⃣ Testing parameters:`, params);
    
    const searchParams = new URLSearchParams(params);
    const testURL = `/tours?${searchParams.toString()}`;
    
    console.log('📍 Generated URL:', testURL);
  });
  
  console.log('✅ URL parameter generation working');
  return true;
};

// Test Firebase data integration
export const testFirebaseIntegration = async () => {
  console.log('🔥 Testing Firebase Data Integration...');

  try {
    // Test 1: Load destinations from Firebase
    console.log('1️⃣ Testing destination loading...');
    const destinations = await DestinationService.getDestinationNames();
    console.log('✅ Destinations loaded:', destinations.length);
    console.log('📍 Sample destinations:', destinations.slice(0, 5));

    // Test 2: Load popular destinations
    console.log('2️⃣ Testing popular destinations...');
    const popular = await DestinationService.getPopularDestinations();
    console.log('✅ Popular destinations loaded:', popular.length);
    console.log('🌟 Popular destinations:', popular.map(p => p.name));

    // Test 3: Test destination validation
    console.log('3️⃣ Testing destination validation...');
    const validDestination = destinations[0];
    const isValid = await DestinationService.validateDestination(validDestination);
    console.log(`✅ Validation test: "${validDestination}" is ${isValid ? 'valid' : 'invalid'}`);

    // Test 4: Test destination suggestions
    console.log('4️⃣ Testing destination suggestions...');
    const suggestions = await DestinationService.getDestinationSuggestions('Seren', 3);
    console.log('✅ Suggestions for "Seren":', suggestions);

    // Test 5: Test destination stats
    console.log('5️⃣ Testing destination statistics...');
    const stats = await DestinationService.getDestinationStats();
    console.log('✅ Destination stats:', stats);

    console.log('🎉 All Firebase integration tests passed!');
    return { success: true, destinations, popular, stats };

  } catch (error) {
    console.error('❌ Firebase integration test failed:', error);
    return { success: false, error: error.message };
  }
};

// Comprehensive test runner
export const runQuickSearchTests = async () => {
  console.log('🧪 Running Quick Search Form Tests...');
  console.log('=====================================');

  const results = {
    formElements: testQuickSearchForm(),
    popularTags: testPopularTags(),
    urlParameters: testURLParameters(),
    firebaseIntegration: await testFirebaseIntegration()
  };

  const passedTests = Object.values(results).filter(result =>
    typeof result === 'boolean' ? result : result.success
  ).length;
  const totalTests = Object.keys(results).length;

  console.log('=====================================');
  console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All Quick Search Form tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above.');
  }

  return results;
};

// Make functions available globally for browser console testing
(window as any).testQuickSearch = {
  testQuickSearchForm,
  testPopularTags,
  testURLParameters,
  testFirebaseIntegration,
  runQuickSearchTests
};
