import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  MapPin, 
  Star, 
  Clock, 
  Camera, 
  Thermometer, 
  Shield, 
  Users, 
  Calendar,
  ArrowLeft,
  Plane,
  TreePine,
  Heart,
  Info
} from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Destination } from '@/types/firebase';

const DestinationDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [destination, setDestination] = useState<Destination | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    if (id) {
      loadDestination(id);
    }
  }, [id]);

  const loadDestination = async (destinationId: string) => {
    try {
      const data = await FirebaseService.getDestination(destinationId);
      setDestination(data as Destination);
    } catch (error) {
      console.error('Error loading destination:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="relative h-96">
            <Skeleton className="w-full h-full" />
          </div>
          <div className="container mx-auto px-4 py-8">
            <Skeleton className="h-8 w-64 mb-4" />
            <Skeleton className="h-4 w-full mb-8" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Skeleton className="h-64 w-full" />
              </div>
              <div>
                <Skeleton className="h-48 w-full" />
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!destination) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="container mx-auto px-4 py-16 text-center">
            <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-600 mb-2">Destination Not Found</h1>
            <p className="text-gray-500 mb-8">The destination you're looking for doesn't exist.</p>
            <Button asChild>
              <Link to="/destinations">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Destinations
              </Link>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const defaultImages = [
    'photo-1472396961693-142e6e269027',
    'photo-1466721591366-2d5fba72006d',
    'photo-1547036967-23d11aacaee0',
    'photo-1493962853295-0fd70327578a'
  ];

  const images = destination.images && destination.images.length > 0 ? destination.images : defaultImages;

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section with Image Gallery */}
        <div className="relative h-96 md:h-[500px] overflow-hidden">
          <img
            src={`https://images.unsplash.com/${images[selectedImageIndex]}?auto=format&fit=crop&w=1920&h=800`}
            alt={destination.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          
          {/* Navigation */}
          <div className="absolute top-6 left-6">
            <Button variant="outline" size="sm" asChild className="bg-white/90 backdrop-blur-sm">
              <Link to="/destinations">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Destinations
              </Link>
            </Button>
          </div>

          {/* Title and Location */}
          <div className="absolute bottom-8 left-8 right-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl md:text-5xl font-bold text-white mb-2">
                  {destination.name}
                </h1>
                <p className="text-white/90 text-lg flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  {destination.region}, {destination.country}
                </p>
              </div>
              {destination.featured && (
                <Badge className="bg-yellow-500 text-black text-lg px-4 py-2">
                  <Star className="h-4 w-4 mr-2" />
                  Featured
                </Badge>
              )}
            </div>
          </div>

          {/* Image Thumbnails */}
          {images.length > 1 && (
            <div className="absolute bottom-4 right-8 flex gap-2">
              {images.slice(0, 4).map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`w-16 h-12 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImageIndex === index ? 'border-white' : 'border-white/50'
                  }`}
                >
                  <img
                    src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=100&h=75`}
                    alt={`${destination.name} view ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="wildlife">Wildlife</TabsTrigger>
                  <TabsTrigger value="activities">Activities</TabsTrigger>
                  <TabsTrigger value="guide">Travel Guide</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Info className="h-5 w-5 mr-2" />
                        About {destination.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed mb-4">
                        {destination.description}
                      </p>
                      {destination.detailedGuide?.overview && (
                        <p className="text-gray-700 leading-relaxed">
                          {destination.detailedGuide.overview}
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  {destination.detailedGuide?.geography && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <TreePine className="h-5 w-5 mr-2" />
                          Geography & Landscape
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700 leading-relaxed">
                          {destination.detailedGuide.geography}
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {destination.detailedGuide?.history && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Historical Background</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700 leading-relaxed">
                          {destination.detailedGuide.history}
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="wildlife" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Camera className="h-5 w-5 mr-2" />
                        Wildlife Species ({destination.wildlife?.length || 0})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {destination.wildlife && destination.wildlife.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {destination.wildlife.map((animal, index) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="flex justify-between items-start mb-2">
                                <h4 className="font-semibold text-lg">{animal.species}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {animal.abundance}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 italic mb-2">
                                {animal.scientificName}
                              </p>
                              <p className="text-sm text-gray-700 mb-2">
                                {animal.behavior}
                              </p>
                              <div className="flex justify-between items-center text-xs text-gray-500">
                                <span>Best Time: {animal.bestSpottingTime}</span>
                                <Badge 
                                  variant={animal.conservationStatus === 'Endangered' ? 'destructive' : 'secondary'}
                                  className="text-xs"
                                >
                                  {animal.conservationStatus}
                                </Badge>
                              </div>
                              {animal.photographyTips && (
                                <p className="text-xs text-blue-600 mt-2">
                                  📸 {animal.photographyTips}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500">Wildlife information not available.</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="activities" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Available Activities</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {destination.activities && destination.activities.length > 0 ? (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {destination.activities.map((activity, index) => (
                            <Badge key={index} variant="outline" className="justify-center py-2">
                              {activity}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500">Activity information not available.</p>
                      )}
                    </CardContent>
                  </Card>

                  {destination.accommodations && destination.accommodations.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Accommodation Options</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {destination.accommodations.map((accommodation, index) => (
                            <div key={index} className="border rounded-lg p-3">
                              <p className="font-medium">{accommodation}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="guide" className="space-y-6">
                  {destination.detailedGuide?.bestTimeToVisit && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <Calendar className="h-5 w-5 mr-2" />
                          Best Time to Visit
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {destination.detailedGuide.bestTimeToVisit.drySeason && (
                          <div>
                            <h4 className="font-semibold text-blue-600 mb-2">Dry Season</h4>
                            <p className="text-gray-700">{destination.detailedGuide.bestTimeToVisit.drySeason}</p>
                          </div>
                        )}
                        {destination.detailedGuide.bestTimeToVisit.greenSeason && (
                          <div>
                            <h4 className="font-semibold text-green-600 mb-2">Green Season</h4>
                            <p className="text-gray-700">{destination.detailedGuide.bestTimeToVisit.greenSeason}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}

                  {destination.detailedGuide?.packingTips && destination.detailedGuide.packingTips.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Packing Tips</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {destination.detailedGuide.packingTips.map((tip, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-2">•</span>
                              <span className="text-gray-700">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {destination.detailedGuide?.travelTips && destination.detailedGuide.travelTips.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Travel Tips</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {destination.detailedGuide.travelTips.map((tip, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-orange-600 mr-2">•</span>
                              <span className="text-gray-700">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center text-sm text-gray-600">
                      <Thermometer className="h-4 w-4 mr-2" />
                      Climate
                    </span>
                    <span className="text-sm font-medium">{destination.climate || 'Tropical'}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      Best Months
                    </span>
                    <span className="text-sm font-medium">
                      {destination.bestTimeToVisit?.join(', ') || 'Year Round'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="flex items-center text-sm text-gray-600">
                      <Camera className="h-4 w-4 mr-2" />
                      Wildlife Species
                    </span>
                    <span className="text-sm font-medium">{destination.wildlife?.length || 0}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      Activities
                    </span>
                    <span className="text-sm font-medium">{destination.activities?.length || 0}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <Card>
                <CardHeader>
                  <CardTitle>Plan Your Visit</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" asChild>
                    <Link to="/tours">
                      <Plane className="h-4 w-4 mr-2" />
                      View Safari Tours
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/tour-builder">
                      <Heart className="h-4 w-4 mr-2" />
                      Custom Tour Builder
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/contact">
                      <Users className="h-4 w-4 mr-2" />
                      Contact Expert
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Conservation Info */}
              {destination.conservationInfo && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="h-5 w-5 mr-2" />
                      Conservation
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {destination.conservationInfo.conservationFee && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-600">Conservation Fee</p>
                        <p className="font-semibold">${destination.conservationInfo.conservationFee}</p>
                      </div>
                    )}
                    {destination.conservationInfo.howTouristsHelp && destination.conservationInfo.howTouristsHelp.length > 0 && (
                      <div>
                        <p className="text-sm text-gray-600 mb-2">How You Can Help</p>
                        <ul className="text-xs space-y-1">
                          {destination.conservationInfo.howTouristsHelp.slice(0, 3).map((help, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-1">•</span>
                              <span>{help}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default DestinationDetail;
