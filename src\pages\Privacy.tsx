
import React, { useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Lock, Eye, Database, Mail, Phone } from 'lucide-react';

const Privacy = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const sections = [
    {
      icon: Database,
      title: 'Information We Collect',
      content: `We collect information you provide directly to us, such as when you create an account, make a booking, or contact us. This includes your name, email address, phone number, travel preferences, and payment information. We also automatically collect certain information when you use our services, including your IP address, browser type, and usage patterns.`
    },
    {
      icon: Eye,
      title: 'How We Use Your Information',
      content: `We use your information to provide and improve our safari services, process bookings, communicate with you about your trips, send marketing communications (with your consent), and ensure the safety and security of our services. We may also use aggregated, anonymized data for research and analytics purposes.`
    },
    {
      icon: Shield,
      title: 'Information Sharing',
      content: `We do not sell, trade, or rent your personal information to third parties. We may share your information with trusted service providers who assist us in operating our business, such as payment processors and booking platforms, but only to the extent necessary to provide our services. We may also disclose information when required by law or to protect our rights and safety.`
    },
    {
      icon: Lock,
      title: 'Data Security',
      content: `We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. This includes encryption of sensitive data, secure servers, and regular security audits. However, no method of transmission over the internet is 100% secure.`
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-64 overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-80"
            style={{
              backgroundImage: 'url(https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/80 to-cyan-600/80" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-3xl mx-auto">
              <Badge className="mb-4 bg-white/20 text-white px-4 py-2">
                <Shield className="w-4 h-4 mr-2" />
                Privacy Policy
              </Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Your Privacy Matters</h1>
              <p className="text-lg md:text-xl opacity-90">
                Learn how we protect and handle your personal information
              </p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="mb-12 shadow-lg border-0">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold mb-4">Privacy Policy</h2>
                  <p className="text-gray-600 text-lg">Effective Date: January 1, 2024</p>
                </div>
                <div className="prose prose-lg max-w-none text-gray-700">
                  <p>
                    At SafariSole Tours, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services.
                  </p>
                  <p>
                    By using our services, you consent to the collection and use of your information as described in this policy. If you do not agree with our policies and practices, please do not use our services.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Main Sections */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {sections.map((section, index) => (
                <Card key={index} className="shadow-lg border-0 hover:shadow-xl transition-shadow">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50">
                    <CardTitle className="flex items-center text-xl">
                      <div className="bg-blue-100 p-2 rounded-lg mr-3">
                        <section.icon className="h-6 w-6 text-blue-600" />
                      </div>
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className="text-gray-700 leading-relaxed">{section.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Additional Sections */}
            <div className="space-y-8">
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Your Rights and Choices</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Access and Update</h4>
                      <p className="text-gray-700">You have the right to access and update your personal information at any time through your account settings.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Data Portability</h4>
                      <p className="text-gray-700">You can request a copy of your personal data in a structured, machine-readable format.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Deletion</h4>
                      <p className="text-gray-700">You can request deletion of your personal information, subject to certain legal obligations.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Marketing Communications</h4>
                      <p className="text-gray-700">You can opt out of marketing communications at any time by clicking the unsubscribe link in our emails.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Cookies and Tracking</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700 mb-4">
                    We use cookies and similar tracking technologies to enhance your browsing experience, analyze website traffic, and understand where our visitors are coming from. You can control cookie preferences through your browser settings.
                  </p>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Types of Cookies We Use:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      <li>Essential cookies for website functionality</li>
                      <li>Analytics cookies to understand user behavior</li>
                      <li>Marketing cookies for personalized advertising</li>
                      <li>Preference cookies to remember your settings</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">International Data Transfers</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700">
                    As a Tanzania-based company serving international clients, we may transfer your personal information to countries other than your own. We ensure that such transfers are conducted in accordance with applicable data protection laws and implement appropriate safeguards to protect your information.
                  </p>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Changes to This Policy</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700">
                    We may update this Privacy Policy from time to time to reflect changes in our practices or applicable laws. We will notify you of any material changes by posting the updated policy on our website and updating the "Effective Date" at the top of this policy.
                  </p>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-2xl text-orange-800">Contact Us</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-orange-700 mb-4">
                    If you have any questions about this Privacy Policy or our data practices, please contact us:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-orange-600" />
                      <span className="text-orange-700"><EMAIL></span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-orange-600" />
                      <span className="text-orange-700">+255 784 123 456</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Privacy;
