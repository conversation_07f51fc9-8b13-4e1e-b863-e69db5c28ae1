
import React from 'react';
import Header from '@/components/layout/Header';
import HeroSection from '@/components/home/<USER>';
import ValuePropositions from '@/components/home/<USER>';
import FeaturedTours from '@/components/home/<USER>';
import DestinationShowcase from '@/components/home/<USER>';
import { TestimonialsSection } from '@/components/ui/testimonials-with-marquee'; // Updated import
import Footer from '@/components/layout/Footer';
import WeatherWidget from '@/components/features/WeatherWidget';
import SustainabilityDashboard from '@/components/features/SustainabilityDashboard';
import { FlickeringGrid } from '@/components/ui/flickering-grid';

import '@/utils/testWeatherService';

const Index = () => {

  const testimonialsData = [
    {
      author: {
        name: '<PERSON>',
        handle: '@sarah_travels',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face'
      },
      text: 'Absolutely incredible experience! The guides were knowledgeable, the accommodations were luxurious, and seeing the Great Migration was a dream come true.',
      href: '#' // Optional link to a full review or social media post
    },
    {
      author: {
        name: '<PERSON>',
        handle: '@mike_adventures',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
      },
      text: 'The attention to detail was phenomenal. Every day brought new adventures and the wildlife viewing exceeded all expectations. Highly recommend Warriors of Africa Safari!',
    },
    {
      author: {
        name: 'Emma Rodriguez',
        handle: '@emma_explores',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
      },
      text: 'The perfect blend of adventure and comfort. The cultural experiences with local communities were as memorable as the wildlife encounters. A truly unforgettable trip!',
      href: '#'
    },
    {
      author: {
        name: 'David Miller',
        handle: '@dave_on_safari',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
      },
      text: 'From the moment we landed to our departure, everything was seamless. The team is passionate and it shows. Can\'t wait for our next safari with them!',
    }
  ];

  return (
    <div className="min-h-screen relative">
    
      <Header />
      <main>
        <HeroSection />
        <ValuePropositions />
        
        {/* New Features Section */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4">Smart Safari Planning</h2>
              <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Experience our advanced features designed to make your safari planning intelligent and effortless
              </p>
            </div>
            
            <div className="mb-8 md:mb-12">
              <WeatherWidget />
            </div>
            
            <div className="flex justify-center">
              <SustainabilityDashboard />
            </div>
          </div>
        </section>
        
        <FeaturedTours />
        <DestinationShowcase />
        <TestimonialsSection
          title="What Our Travelers Say"
          description="Don't just take our word for it. Here's what our guests have to say about their unforgettable safari experiences."
          testimonials={testimonialsData}
          className="bg-gray-50" // Example: using a light background
        />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
