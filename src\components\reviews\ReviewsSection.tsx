
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Star, ThumbsUp, MessageCircle, Shield, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Review } from '@/types/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Timestamp } from 'firebase/firestore';

interface ReviewsSectionProps {
  tourId: string;
  tourName: string;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({ tourId, tourName }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const { currentUser } = useAuth();
  const { toast } = useToast();

  const [newReview, setNewReview] = useState({
    rating: 5,
    title: '',
    content: ''
  });

  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await FirebaseService.getReviews(tourId);
        setReviews(reviewsData as Review[]);
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast({
          title: "Error loading reviews",
          description: "There was an error loading the reviews.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadReviews();
  }, [tourId, toast]);

  const handleSubmitReview = async () => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to leave a review.",
        variant: "destructive"
      });
      return;
    }

    if (!newReview.title.trim() || !newReview.content.trim()) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      
      const reviewData = {
        tourId: tourId,
        tourName: tourName,
        userId: currentUser.uid,
        userName: currentUser.displayName || currentUser.email || 'Anonymous User',
        userAvatar: currentUser.photoURL || '',
        rating: newReview.rating,
        title: newReview.title,
        content: newReview.content,
        images: [],
        verified: false,
        helpful: 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await FirebaseService.createReview(reviewData);
      
      toast({
        title: "Review submitted",
        description: "Thank you for your review!"
      });

      // Reset form
      setNewReview({ rating: 5, title: '', content: '' });
      setShowReviewForm(false);
      
      // Reload reviews
      const updatedReviews = await FirebaseService.getReviews(tourId);
      setReviews(updatedReviews as Review[]);
      
    } catch (error) {
      console.error('Error submitting review:', error);
      toast({
        title: "Error submitting review",
        description: "There was an error submitting your review. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const RatingStars = ({ rating, size = 'w-4 h-4', interactive = false, onRatingChange }: { 
    rating: number; 
    size?: string; 
    interactive?: boolean;
    onRatingChange?: (rating: number) => void;
  }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'} ${
            interactive ? 'cursor-pointer hover:text-yellow-400' : ''
          }`}
          onClick={interactive ? () => onRatingChange?.(star) : undefined}
        />
      ))}
    </div>
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';
    try {
      return timestamp.toDate().toLocaleDateString();
    } catch {
      return 'Unknown date';
    }
  };

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading reviews...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2 mb-2">
                Reviews ({reviews.length})
                {averageRating > 0 && (
                  <div className="flex items-center gap-1">
                    <RatingStars rating={Math.round(averageRating)} />
                    <span className="text-sm font-normal">({averageRating.toFixed(1)})</span>
                  </div>
                )}
              </CardTitle>
            </div>
            {currentUser && (
              <Button
                onClick={() => setShowReviewForm(!showReviewForm)}
                className="bg-orange-600 hover:bg-orange-700"
              >
                Write a Review
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <Card>
          <CardHeader>
            <CardTitle>Share Your Experience</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Rating</Label>
              <div className="mt-2">
                <RatingStars 
                  rating={newReview.rating} 
                  size="w-6 h-6"
                  interactive
                  onRatingChange={(rating) => setNewReview(prev => ({ ...prev, rating }))}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="reviewTitle">Review Title</Label>
              <Input
                id="reviewTitle"
                value={newReview.title}
                onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Summarize your experience..."
                maxLength={100}
              />
            </div>
            
            <div>
              <Label htmlFor="reviewContent">Your Review</Label>
              <Textarea
                id="reviewContent"
                value={newReview.content}
                onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Tell others about your safari experience..."
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-gray-500 mt-1">
                {newReview.content.length}/1000 characters
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={handleSubmitReview}
                disabled={submitting}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {submitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Review'
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowReviewForm(false)}
                disabled={submitting}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      {reviews.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-gray-400 mb-4">
              <Star className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No reviews yet</h3>
            <p className="text-gray-600">Be the first to share your experience!</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4 mb-4">
                  <Avatar className="h-10 w-10">
                    {review.userAvatar ? (
                      <img src={review.userAvatar} alt={review.userName || 'User'} className="object-cover" />
                    ) : (
                      <div className="w-full h-full bg-orange-100 flex items-center justify-center text-orange-600 font-semibold">
                        {(review.userName || 'U').charAt(0).toUpperCase()}
                      </div>
                    )}
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">{review.userName || 'Anonymous User'}</h4>
                      {review.verified && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          <Shield className="w-3 h-3 mr-1" />
                          Verified
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <RatingStars rating={review.rating} />
                      <span>{formatDate(review.createdAt)}</span>
                    </div>
                    <h5 className="font-medium mb-2">{review.title}</h5>
                    <p className="text-gray-700">{review.content}</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 pt-4 border-t">
                  <Button variant="ghost" size="sm">
                    <ThumbsUp className="w-4 h-4 mr-2" />
                    Helpful ({review.helpful || 0})
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Reply
                  </Button>
                </div>

                {review.response && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="font-semibold text-sm mb-1">{review.response.author}</div>
                    <p className="text-sm text-gray-700">{review.response.content}</p>
                    <div className="text-xs text-gray-500 mt-2">{review.response.date}</div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewsSection;
