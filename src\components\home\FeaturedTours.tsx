
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Clock, Users, MapPin, Heart, Camera, ArrowRight, Loader2 } from 'lucide-react';
import WishlistButton from '@/components/features/WishlistButton';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';

const FeaturedTours = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedTours = async () => {
      try {
        setLoading(true);
        const allTours = await FirebaseService.getTours();

        // Filter for featured tours and limit to 3
        const featuredTours = allTours
          .filter((tour: any) => tour.featured === true)
          .slice(0, 3);

        setTours(featuredTours);
      } catch (err) {
        console.error('Error fetching featured tours:', err);
        setError('Failed to load featured tours');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedTours();
  }, []);

  // Helper function to get tour badge
  const getTourBadge = (tour: Tour) => {
    if (tour.tourType === 'luxury') return 'Luxury';
    if (tour.rating >= 4.8) return 'Best Seller';
    if (tour.reviewCount > 100) return 'Popular';
    return 'Featured';
  };

  // Helper function to get group size display
  const getGroupSizeDisplay = (tour: Tour) => {
    return `${tour.maxGroupSize} people max`;
  };

  // Helper function to get tour highlights
  const getTourHighlights = (tour: Tour) => {
    return tour.destinations.slice(0, 3);
  };

  // Helper function to get tour image
  const getTourImage = (tour: Tour) => {
    if (tour.images && tour.images.length > 0) {
      // Check if it's already a full URL
      if (tour.images[0].includes('unsplash.com')) {
        return tour.images[0];
      }
      // Otherwise, treat it as an Unsplash ID
      return tour.images[0];
    }
    // Fallback image
    return 'photo-1472396961693-142e6e269027';
  };

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-orange-100 text-orange-800 px-4 py-2">
              <Camera className="w-4 h-4 mr-2" />
              Featured Tours
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Featured Safari <span className="text-orange-600">Tours</span>
            </h2>
          </div>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-orange-600" />
              <p className="text-gray-600">Loading featured tours...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-orange-100 text-orange-800 px-4 py-2">
              <Camera className="w-4 h-4 mr-2" />
              Featured Tours
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Featured Safari <span className="text-orange-600">Tours</span>
            </h2>
          </div>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f97316' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-orange-100 text-orange-800 px-4 py-2">
            <Camera className="w-4 h-4 mr-2" />
            Featured Tours
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Featured Safari <span className="text-orange-600">Tours</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover our most popular safari experiences, carefully crafted to showcase 
            the best of Tanzania's wildlife and landscapes with expert guides and premium accommodations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {tours.map((tour) => {
            const tourBadge = getTourBadge(tour);
            const tourImage = getTourImage(tour);
            const tourHighlights = getTourHighlights(tour);

            return (
              <div
                key={tour.id}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden hover:-translate-y-3"
              >
                {/* Image */}
                <div className="relative overflow-hidden h-56">
                  <img
                    src={`https://images.unsplash.com/${tourImage}?auto=format&fit=crop&w=500&h=300`}
                    alt={tour.title}
                    className="h-full w-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />

                  {/* Badge */}
                  <Badge
                    className={`absolute top-4 left-4 px-3 py-1 text-sm font-semibold ${
                      tourBadge === 'Best Seller' ? 'bg-red-500 hover:bg-red-600' :
                      tourBadge === 'Popular' ? 'bg-blue-500 hover:bg-blue-600' :
                      tourBadge === 'Luxury' ? 'bg-purple-500 hover:bg-purple-600' : 'bg-green-500 hover:bg-green-600'
                    } text-white shadow-lg`}
                  >
                    {tourBadge}
                  </Badge>

                {/* Wishlist Button */}
                <div className="absolute top-4 right-4">
                  <WishlistButton
                    item={{
                      id: tour.id,
                      title: tour.title,
                      price: tour.price,
                      image: `https://images.unsplash.com/${tourImage}?auto=format&fit=crop&w=500&h=300`,
                      type: 'tour' as const
                    }}
                    size="icon"
                  />
                </div>

                {/* Price Overlay */}
                <div className="absolute bottom-4 right-4 bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg">
                  <div className="text-right">
                    <div className="text-xl font-bold text-orange-600">
                      ${tour.price}
                    </div>
                    <div className="text-xs text-gray-500">per person</div>
                  </div>
                </div>

                {/* Quick View Button */}
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button 
                    asChild
                    className="bg-white text-gray-900 hover:bg-gray-100 shadow-lg"
                  >
                    <Link to={`/tours/${tour.id}`}>
                      <Camera className="mr-2 h-4 w-4" />
                      Quick View
                    </Link>
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors leading-tight">
                    {tour.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {tour.description}
                  </p>
                </div>

                {/* Tour Details */}
                <div className="flex items-center justify-between mb-6 text-sm">
                  <div className="flex items-center text-gray-500">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="font-medium">{tour.duration}</span>
                  </div>
                  <div className="flex items-center text-gray-500">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="font-medium">{getGroupSizeDisplay(tour)}</span>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="flex items-center mr-3">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(tour.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                        />
                      ))}
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mr-1">
                      {tour.rating}
                    </span>
                    <span className="text-sm text-gray-500">
                      ({tour.reviewCount} reviews)
                    </span>
                  </div>
                </div>

                {/* Highlights */}
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    {tourHighlights.map((highlight, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs px-3 py-1 border-orange-200 text-orange-700 bg-orange-50 hover:bg-orange-100 transition-colors"
                      >
                        {highlight}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button 
                    asChild
                    className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all"
                  >
                    <Link to={`/tours/${tour.id}`}>
                      View Details
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button 
                    asChild
                    variant="outline" 
                    className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white transition-colors"
                  >
                    <Link to={`/book/${tour.id}`}>
                      Book Now
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">Ready for Your Safari Adventure?</h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Explore all our safari packages or create a custom itinerary tailored to your dreams
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-orange-600 transition-colors"
            >
              <Link to="/tours">
                <MapPin className="mr-2 h-5 w-5" />
                View All Tours
              </Link>
            </Button>
            <Button 
              asChild
              size="lg" 
              className="bg-white text-orange-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all"
            >
              <Link to="/tour-builder">
                <Camera className="mr-2 h-5 w-5" />
                Create Custom Tour
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedTours;
