import { WeatherService } from '@/services/weatherService';

export const testWeatherService = async () => {
  console.log('🌤️ Testing Weather Service...');
  
  try {
    // Test 1: Get current weather for a single location
    console.log('1️⃣ Testing current weather for Serengeti...');
    const currentWeather = await WeatherService.getCurrentWeather('Serengeti National Park, Tanzania');
    console.log('✅ Current weather:', {
      location: currentWeather.location.name,
      temperature: currentWeather.current.temp_c,
      condition: currentWeather.current.condition.text,
      humidity: currentWeather.current.humidity,
      windSpeed: currentWeather.current.wind_kph
    });
    
    // Test 2: Get forecast for a location
    console.log('2️⃣ Testing forecast for Ngorongoro...');
    const forecast = await WeatherService.getForecast('Ngorongoro Crater, Tanzania', 3);
    console.log('✅ Forecast:', {
      location: forecast.location.name,
      days: forecast.forecast?.forecastday.length,
      firstDay: forecast.forecast?.forecastday[0]?.day.condition.text
    });
    
    // Test 3: Get weather for all safari locations
    console.log('3️⃣ Testing safari weather for all locations...');
    const safariWeather = await WeatherService.getSafariWeather();
    console.log('✅ Safari weather loaded for', safariWeather.length, 'locations');
    
    // Test 4: Format weather data
    console.log('4️⃣ Testing weather data formatting...');
    const formattedData = WeatherService.formatWeatherData(currentWeather);
    console.log('✅ Formatted data:', {
      location: formattedData.location,
      temperature: formattedData.temperature,
      recommendation: formattedData.recommendation,
      forecastDays: formattedData.forecast.length
    });
    
    console.log('🎉 All weather service tests passed!');
    return { success: true, message: 'Weather service is working correctly' };
    
  } catch (error) {
    console.error('❌ Weather service test failed:', error);
    return { success: false, error: error.message };
  }
};

// Test function that can be called from browser console
(window as any).testWeatherService = testWeatherService;
