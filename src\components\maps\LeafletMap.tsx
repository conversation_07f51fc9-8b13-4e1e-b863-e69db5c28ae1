import React, { useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin } from 'lucide-react';

interface Destination {
  id: string;
  name: string;
  lat: number;
  lng: number;
  description: string;
  image: string;
}

interface LeafletMapProps {
  destinations: Destination[];
  onDestinationClick?: (destination: Destination) => void;
  showRoutes?: boolean;
  height?: string;
}

const LeafletMap: React.FC<LeafletMapProps> = ({ 
  destinations, 
  onDestinationClick, 
  showRoutes = false,
  height = "400px" 
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  useEffect(() => {
    // Dynamically import Leaflet to avoid SSR issues
    const initializeMap = async () => {
      if (!mapRef.current) return;

      try {
        const L = await import('leaflet');
        
        // Fix for default markers in Leaflet with webpack
        delete (L.Icon.Default.prototype as any)._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        // Initialize map
        const map = L.map(mapRef.current).setView([-3.5, 35.0], 7);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Custom marker icon
        const customIcon = L.divIcon({
          html: `<div style="background-color: #ea580c; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
                   <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                     <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                     <circle cx="12" cy="10" r="3"/>
                   </svg>
                 </div>`,
          className: 'custom-div-icon',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });

        // Add markers for each destination
        destinations.forEach(destination => {
          const marker = L.marker([destination.lat, destination.lng], { icon: customIcon })
            .addTo(map);

          // Create popup content
          const popupContent = `
            <div style="padding: 10px; max-width: 200px;">
              <img src="https://images.unsplash.com/${destination.image}?auto=format&fit=crop&w=200&h=120" 
                   style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;" />
              <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 16px; font-weight: bold;">${destination.name}</h3>
              <p style="margin: 0; color: #6b7280; font-size: 14px; line-height: 1.4;">${destination.description}</p>
            </div>
          `;

          marker.bindPopup(popupContent);

          // Add click handler
          marker.on('click', () => {
            if (onDestinationClick) {
              onDestinationClick(destination);
            }
          });
        });

        // Store map instance
        mapInstanceRef.current = map;

        // Handle routes if enabled
        if (showRoutes && destinations.length > 1) {
          const latlngs = destinations.map(dest => [dest.lat, dest.lng] as [number, number]);
          L.polyline(latlngs, { 
            color: '#ea580c', 
            weight: 3, 
            opacity: 0.8 
          }).addTo(map);
        }

      } catch (error) {
        console.error('Error initializing Leaflet map:', error);
      }
    };

    initializeMap();

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [destinations, onDestinationClick, showRoutes]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MapPin className="mr-2 h-5 w-5 text-orange-600" />
          Interactive Safari Map
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={mapRef} 
          style={{ height, width: '100%' }}
          className="rounded-lg border border-gray-200"
        />
        <div className="mt-4 text-sm text-gray-600">
          Click on any marker to see destination details and tour options.
        </div>
      </CardContent>
    </Card>
  );
};

export default LeafletMap;
