
import React, { useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, Scale, AlertTriangle, CreditCard, RefreshCw, Mail, Phone } from 'lucide-react';

const Terms = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const sections = [
    {
      icon: CreditCard,
      title: 'Booking and Payment',
      content: `All bookings require a deposit to secure your reservation. Full payment is due 30 days before departure. We accept major credit cards and bank transfers. Prices are subject to change until booking is confirmed. Currency exchange rate fluctuations may affect final pricing for international clients.`
    },
    {
      icon: RefreshCw,
      title: 'Cancellation Policy',
      content: `Cancellations made 60+ days before departure: 10% penalty. 30-59 days: 50% penalty. 15-29 days: 75% penalty. Less than 15 days: 100% penalty. We recommend travel insurance to protect against unforeseen circumstances. Cancellations due to government travel restrictions may qualify for full refund.`
    },
    {
      icon: AlertTriangle,
      title: 'Liability and Insurance',
      content: `SafariSole Tours acts as an agent for various service providers. While we carefully select our partners, we are not liable for their actions or omissions. Comprehensive travel insurance is strongly recommended. Participants engage in activities at their own risk and must follow safety guidelines provided by our guides.`
    },
    {
      icon: Scale,
      title: 'Governing Law',
      content: `These terms are governed by the laws of Tanzania. Any disputes will be resolved through binding arbitration in Arusha, Tanzania. By booking with us, you acknowledge that wildlife viewing is subject to natural conditions and sightings cannot be guaranteed. Weather and seasonal variations may affect itineraries.`
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-64 overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-80"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1485833077593-4278bba3f11f?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-green-600/80 to-emerald-600/80" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-3xl mx-auto">
              <Badge className="mb-4 bg-white/20 text-white px-4 py-2">
                <FileText className="w-4 h-4 mr-2" />
                Terms of Service
              </Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Terms & Conditions</h1>
              <p className="text-lg md:text-xl opacity-90">
                Please read our terms carefully before booking your safari
              </p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="mb-12 shadow-lg border-0">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold mb-4">Terms of Service</h2>
                  <p className="text-gray-600 text-lg">Effective Date: January 1, 2024</p>
                </div>
                <div className="prose prose-lg max-w-none text-gray-700">
                  <p>
                    Welcome to SafariSole Tours. These Terms of Service ("Terms") govern your use of our website and services. By accessing our website or booking a safari with us, you agree to be bound by these Terms.
                  </p>
                  <p>
                    Please read these Terms carefully before making any bookings. If you do not agree with any part of these Terms, you should not use our services.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Main Sections */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {sections.map((section, index) => (
                <Card key={index} className="shadow-lg border-0 hover:shadow-xl transition-shadow">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50">
                    <CardTitle className="flex items-center text-xl">
                      <div className="bg-green-100 p-2 rounded-lg mr-3">
                        <section.icon className="h-6 w-6 text-green-600" />
                      </div>
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className="text-gray-700 leading-relaxed">{section.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Additional Detailed Sections */}
            <div className="space-y-8">
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Safari Participation Requirements</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Age Requirements</h4>
                      <p className="text-gray-700">Children under 12 must be accompanied by an adult. Some activities may have age restrictions for safety reasons.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Health and Fitness</h4>
                      <p className="text-gray-700">Participants should be in reasonable physical condition. Medical conditions must be disclosed at time of booking.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Documentation</h4>
                      <p className="text-gray-700">Valid passport with at least 6 months validity and appropriate visas are required. Yellow fever vaccination may be mandatory.</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Behavior Standards</h4>
                      <p className="text-gray-700">Participants must follow guide instructions and respect wildlife, local communities, and other guests.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Itinerary Changes</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="bg-yellow-50 p-4 rounded-lg mb-4">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-yellow-800 mb-2">Important Notice</h4>
                        <p className="text-yellow-700">Safari itineraries may be modified due to weather, wildlife movements, road conditions, or other circumstances beyond our control.</p>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">
                    We reserve the right to alter itineraries for safety or operational reasons. Alternative activities of equal value will be provided when possible. No refunds will be given for itinerary changes due to circumstances beyond our control.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold">Factors that may affect itineraries:</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      <li>Weather conditions and seasonal variations</li>
                      <li>Wildlife migration patterns</li>
                      <li>Road accessibility and maintenance</li>
                      <li>Government regulations and park closures</li>
                      <li>Force majeure events</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Accommodations and Services</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700 mb-4">
                    Accommodation standards are based on local criteria and may differ from international standards. We work with carefully selected partners to ensure quality service, but cannot guarantee specific room assignments or hotel amenities.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Included Services</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        <li>Professional safari guide</li>
                        <li>Game drive vehicles</li>
                        <li>Meals as specified</li>
                        <li>Park fees and permits</li>
                        <li>Airport transfers</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Not Included</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                        <li>International flights</li>
                        <li>Travel insurance</li>
                        <li>Personal expenses</li>
                        <li>Gratuities</li>
                        <li>Optional activities</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Intellectual Property</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700">
                    All content on our website, including text, images, logos, and designs, is protected by copyright and other intellectual property laws. You may not reproduce, distribute, or use our content without written permission. Photos taken during your safari may be used by SafariSole Tours for marketing purposes unless you opt out in writing.
                  </p>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-2xl">Dispute Resolution</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700 mb-4">
                    We are committed to resolving any issues promptly and fairly. If you have concerns during your safari, please inform your guide immediately. For post-trip complaints, contact us within 30 days of your return.
                  </p>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Resolution Process:</h4>
                    <ol className="list-decimal list-inside space-y-1 text-gray-700">
                      <li>Direct communication with our customer service team</li>
                      <li>Formal complaint review within 14 business days</li>
                      <li>Mediation through Tanzania Tourism Association if needed</li>
                      <li>Binding arbitration as final resort</li>
                    </ol>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-2xl text-orange-800">Questions About These Terms?</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-orange-700 mb-4">
                    If you have any questions about these Terms of Service, please don't hesitate to contact us:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-orange-600" />
                      <span className="text-orange-700"><EMAIL></span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-orange-600" />
                      <span className="text-orange-700">+255 784 123 456</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Terms;
