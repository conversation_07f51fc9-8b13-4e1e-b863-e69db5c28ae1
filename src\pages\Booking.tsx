
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import EnhancedBookingForm from '@/components/booking/EnhancedBookingForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, MapPin, Users, Star, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
  dietaryRestrictions: string[];
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

const Booking = () => {
  const { tourId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [tour, setTour] = useState<Tour | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  
  const [bookingData, setBookingData] = useState<BookingData>({
    tourId: tourId || '',
    startDate: null,
    groupSize: 1,
    travelers: [{
      firstName: '',
      lastName: '',
      email: currentUser?.email || '',
      phone: '',
      dateOfBirth: null,
      passportNumber: '',
      nationality: '',
    }],
    accommodation: 'standard',
    addOns: [],
    specialRequests: '',
    totalPrice: 0,
    dietaryRestrictions: [],
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  });

  useEffect(() => {
    const loadTour = async () => {
      if (!tourId) {
        navigate('/tours');
        return;
      }

      try {
        setLoading(true);
        console.log('Loading tour with ID:', tourId); // Debug log
        const tourData = await FirebaseService.getTour(tourId);
        console.log('Tour data received:', tourData); // Debug log

        if (tourData) {
          setTour(tourData as Tour);
          setBookingData(prev => ({
            ...prev,
             totalPrice: (tourData as Tour).price || 0
          }));
        } else {
          console.error('Tour not found for ID:', tourId); // Debug log
          toast({
            title: "Tour not found",
            description: "The requested tour could not be found. Please check the tour ID and try again.",
            variant: "destructive"
          });
          navigate('/tours');
        }
      } catch (error) {
        console.error('Error loading tour:', error);
        toast({
          title: "Error loading tour",
          description: "There was an error loading the tour details. Please try again later.",
          variant: "destructive"
        });
        navigate('/tours');
      } finally {
        setLoading(false);
      }
    };

    loadTour();
  }, [tourId, navigate, toast]);

  // Check if user is authenticated
  useEffect(() => {
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to make a booking.",
        variant: "destructive"
      });
      navigate('/login');
    }
  }, [currentUser, navigate, toast]);

  const handleDataChange = (field: keyof BookingData, value: any) => {
    setBookingData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Recalculate total price when relevant fields change
      if (field === 'groupSize' || field === 'accommodation' || field === 'addOns') {
        const basePrice = tour?.price || 0;
        const accommodationPrices = {
          budget: 0,
          standard: 200,
          luxury: 500
        };
        const addOnPrices = {
          photography: 75,
          cultural: 50,
          balloon: 550,
          'night-drive': 100
        };
        
        const accommodationCost = accommodationPrices[updated.accommodation as keyof typeof accommodationPrices] || 0;
        const addOnsCost = updated.addOns.reduce((sum, addon) => 
          sum + (addOnPrices[addon as keyof typeof addOnPrices] || 0), 0
        );
        
        updated.totalPrice = (basePrice + accommodationCost) * updated.groupSize + addOnsCost;
      }
      
      return updated;
    });
  };

  const handleNext = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const handlePrev = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleBookingComplete = () => {
    toast({
      title: "Booking Confirmed!",
      description: "Your booking has been successfully submitted. You will receive a confirmation email shortly."
    });
    navigate('/user-dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading booking details...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!tour || !currentUser) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="pt-20 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">Complete Your Booking</h1>
              <p className="text-gray-600">Secure your safari adventure with just a few steps</p>
            </div>

            {/* Progress Bar */}
            <div className="mb-6 md:mb-8">
              <div className="flex items-center justify-between mb-3 md:mb-4">
                {[1, 2, 3, 4].map((stepNumber) => (
                  <div key={stepNumber} className="flex items-center">
                    <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-xs md:text-sm font-semibold ${
                      currentStep >= stepNumber ? 'bg-orange-600 text-white' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {stepNumber}
                    </div>
                    {stepNumber < 4 && (
                      <div className={`w-8 md:w-16 h-1 mx-1 md:mx-2 ${
                        currentStep > stepNumber ? 'bg-orange-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between text-xs md:text-sm text-gray-600">
                <span className="text-center">Dates & Group</span>
                <span className="text-center">Traveler Info</span>
                <span className="text-center">Customize</span>
                <span className="text-center">Payment</span>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
              {/* Main Form */}
              <div className="lg:col-span-2 order-2 lg:order-1">
                <EnhancedBookingForm
                  tourId={tour.id}
                  currentStep={currentStep}
                  bookingData={bookingData}
                  onDataChange={handleDataChange}
                  onNext={handleNext}
                  onPrev={handlePrev}
                  customTour={tour}
                />
              </div>

              {/* Booking Summary Sidebar */}
              <div className="lg:col-span-1 order-1 lg:order-2">
                <Card className="lg:sticky lg:top-24">
                  <CardHeader>
                    <CardTitle className="text-lg">Booking Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <img
                        src={tour.images && tour.images.length > 0 
                          ? `https://images.unsplash.com/${tour.images[0]}?auto=format&fit=crop&w=400&h=200`
                          : 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=400&h=200'
                        }
                        alt={tour.title}
                        className="w-full h-32 object-cover rounded-lg mb-3"
                      />
                      <h3 className="font-semibold">{tour.title}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mt-2">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {tour.duration}
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          Max {tour.maxGroupSize}
                        </div>
                      </div>
                      <div className="flex items-center mt-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        <span className="text-sm">{tour.rating || 0} ({tour.reviewCount || 0} reviews)</span>
                      </div>
                    </div>

                    <div className="border-t pt-4 space-y-2">
                      <div className="flex justify-between">
                        <span>Base price per person</span>
                        <span>${tour.price.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Group size</span>
                        <span>{bookingData.groupSize} {bookingData.groupSize === 1 ? 'person' : 'people'}</span>
                      </div>
                      {bookingData.accommodation !== 'budget' && (
                        <div className="flex justify-between">
                          <span>Accommodation upgrade</span>
                          <span>+${bookingData.accommodation === 'standard' ? '200' : '500'}</span>
                        </div>
                      )}
                      {bookingData.addOns.length > 0 && (
                        <div className="flex justify-between">
                          <span>Add-ons ({bookingData.addOns.length})</span>
                          <span>+${bookingData.addOns.reduce((sum, addon) => {
                            const prices = { photography: 75, cultural: 50, balloon: 550, 'night-drive': 100 };
                            return sum + (prices[addon as keyof typeof prices] || 0);
                          }, 0)}</span>
                        </div>
                      )}
                      <div className="border-t pt-2 flex justify-between font-semibold text-lg">
                        <span>Total</span>
                        <span>${bookingData.totalPrice.toLocaleString()}</span>
                      </div>
                    </div>

                    {bookingData.startDate && (
                      <div className="border-t pt-4">
                        <div className="flex items-center text-sm">
                          <Clock className="w-4 h-4 mr-2 text-gray-500" />
                          <span>Start Date: {bookingData.startDate.toLocaleDateString()}</span>
                        </div>
                      </div>
                    )}

                    <div className="bg-gray-50 p-3 rounded-lg">
                      <h4 className="font-semibold text-sm mb-2">Tour Highlights:</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {tour.includes?.slice(0, 4).map((item, index) => (
                          <li key={index}>• {item}</li>
                        ))}
                        {tour.includes && tour.includes.length > 4 && (
                          <li className="font-medium">+ {tour.includes.length - 4} more included</li>
                        )}
                      </ul>
                    </div>

                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="flex items-center text-blue-800 text-sm">
                        <span className="font-medium">Free cancellation</span>
                      </div>
                      <p className="text-blue-700 text-xs mt-1">
                        Cancel up to 30 days before departure for a full refund
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Booking;
