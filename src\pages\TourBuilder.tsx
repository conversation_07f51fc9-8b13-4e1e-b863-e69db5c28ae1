
import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { MapPin, Calendar, Users, DollarSign, Camera, Tent, Car, Utensils } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';

const TourBuilder = () => {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  
  const [tourData, setTourData] = useState({
    // Basic Info
    duration: 5,
    participants: 2,
    budget: [2000],
    startDate: '',
    
    // Destinations
    destinations: [] as string[],
    
    // Interests
    interests: [] as string[],
    
    // Accommodation
    accommodation: 'midrange',
    
    // Activities
    activities: [] as string[],
    
    // Special Requirements
    specialRequests: '',
    fitnessLevel: 'moderate',
    photographyInterest: false,
    
    // Contact Info
    name: '',
    email: '',
    phone: ''
  });

  const destinations = [
    'Serengeti National Park',
    'Ngorongoro Crater',
    'Tarangire National Park',
    'Lake Manyara',
    'Maasai Mara',
    'Mount Kilimanjaro',
    'Zanzibar',
    'Ruaha National Park'
  ];

  const interests = [
    'Big Five Safari',
    'Great Migration',
    'Bird Watching',
    'Photography',
    'Cultural Experiences',
    'Adventure Sports',
    'Relaxation',
    'Conservation Learning'
  ];

  const activities = [
    'Game Drives',
    'Walking Safaris',
    'Hot Air Balloon',
    'Cultural Village Visits',
    'Night Drives',
    'Bush Camping',
    'Photography Workshops',
    'Conservation Activities'
  ];

  const handleArrayToggle = (array: string[], item: string, field: keyof typeof tourData) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
    
    setTourData(prev => ({
      ...prev,
      [field]: newArray
    }));
  };

  const handleSubmit = async () => {
    if (!tourData.name || !tourData.email) {
      toast({
        title: "Please fill in your contact information",
        variant: "destructive"
      });
      return;
    }

    try {
      // Send the tour request to Firebase
      await FirebaseService.createCustomTourRequest({
        duration: tourData.duration,
        participants: tourData.participants,
        budget: tourData.budget,
        startDate: tourData.startDate,
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        name: tourData.name,
        email: tourData.email,
        phone: tourData.phone
      });

      toast({
        title: "Custom Tour Request Sent!",
        description: "We'll contact you within 24 hours with a personalized itinerary."
      });

      // Reset form or redirect
      setStep(1);
      setTourData({
        duration: 5,
        participants: 2,
        budget: [2000],
        startDate: '',
        destinations: [],
        interests: [],
        accommodation: 'midrange',
        activities: [],
        specialRequests: '',
        fitnessLevel: 'moderate',
        photographyInterest: false,
        name: '',
        email: '',
        phone: ''
      });

    } catch (error) {
      console.error('Error sending custom tour request:', error);
      toast({
        title: "Failed to send request",
        description: "Please try again or contact us directly.",
        variant: "destructive"
      });
    }
  };

  const nextStep = () => setStep(prev => Math.min(prev + 1, 5));
  const prevStep = () => setStep(prev => Math.max(prev - 1, 1));

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="pt-20 pb-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bubblegum  font-bold mb-4">Build Your Dream Safari</h1>
            <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
              Create a personalized safari experience tailored to your interests, budget, and timeline
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                {[1, 2, 3, 4, 5].map((stepNumber) => (
                  <div key={stepNumber} className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold ${
                      step >= stepNumber ? 'bg-orange-600 text-white' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {stepNumber}
                    </div>
                    {stepNumber < 5 && (
                      <div className={`w-16 h-1 mx-2 ${
                        step > stepNumber ? 'bg-orange-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between text-sm text-gray-600">
                <span>Basic Info</span>
                <span>Destinations</span>
                <span>Preferences</span>
                <span>Activities</span>
                <span>Contact</span>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  {step === 1 && <><Calendar className="w-5 h-5 mr-2" />Basic Information</>}
                  {step === 2 && <><MapPin className="w-5 h-5 mr-2" />Choose Destinations</>}
                  {step === 3 && <><Tent className="w-5 h-5 mr-2" />Travel Preferences</>}
                  {step === 4 && <><Camera className="w-5 h-5 mr-2" />Activities & Interests</>}
                  {step === 5 && <><Users className="w-5 h-5 mr-2" />Contact Information</>}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Step 1: Basic Information */}
                {step === 1 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label className="text-base font-semibold mb-3 block">Duration</Label>
                        <div className="space-y-3">
                          <Slider
                            value={[tourData.duration]}
                            onValueChange={(value) => setTourData(prev => ({ ...prev, duration: value[0] }))}
                            max={14}
                            min={3}
                            step={1}
                            className="w-full"
                          />
                          <div className="text-center">
                            <Badge variant="outline" className="text-lg px-4 py-2">
                              {tourData.duration} Days
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <div>
                        <Label className="text-base font-semibold mb-3 block">Participants</Label>
                        <div className="space-y-3">
                          <Slider
                            value={[tourData.participants]}
                            onValueChange={(value) => setTourData(prev => ({ ...prev, participants: value[0] }))}
                            max={12}
                            min={1}
                            step={1}
                            className="w-full"
                          />
                          <div className="text-center">
                            <Badge variant="outline" className="text-lg px-4 py-2">
                              {tourData.participants} {tourData.participants === 1 ? 'Person' : 'People'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-base font-semibold mb-3 block">Budget Range (per person)</Label>
                      <div className="space-y-3">
                        <Slider
                          value={tourData.budget}
                          onValueChange={(value) => setTourData(prev => ({ ...prev, budget: value }))}
                          max={10000}
                          min={500}
                          step={100}
                          className="w-full"
                        />
                        <div className="text-center">
                          <Badge variant="outline" className="text-lg px-4 py-2">
                            ${tourData.budget[0].toLocaleString()} per person
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="startDate" className="text-base font-semibold">Preferred Start Date</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={tourData.startDate}
                        onChange={(e) => setTourData(prev => ({ ...prev, startDate: e.target.value }))}
                        min={new Date().toISOString().split('T')[0]}
                        className="mt-2"
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Destinations */}
                {step === 2 && (
                  <div className="space-y-4">
                    <p className="text-gray-600">Select the destinations you'd like to visit:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {destinations.map((destination) => (
                        <div
                          key={destination}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            tourData.destinations.includes(destination)
                              ? 'border-orange-500 bg-orange-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleArrayToggle(tourData.destinations, destination, 'destinations')}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{destination}</span>
                            <Checkbox
                              checked={tourData.destinations.includes(destination)}
                              onChange={() => {}}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-gray-500 mt-4">
                      Selected {tourData.destinations.length} destination(s)
                    </p>
                  </div>
                )}

                {/* Step 3: Preferences */}
                {step === 3 && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-base font-semibold mb-3 block">Accommodation Level</Label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {[
                          { value: 'budget', label: 'Budget', desc: '$50-150/night' },
                          { value: 'midrange', label: 'Mid-range', desc: '$150-400/night' },
                          { value: 'luxury', label: 'Luxury', desc: '$400+/night' }
                        ].map((option) => (
                          <div
                            key={option.value}
                            className={`p-4 border rounded-lg cursor-pointer transition-all ${
                              tourData.accommodation === option.value
                                ? 'border-orange-500 bg-orange-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => setTourData(prev => ({ ...prev, accommodation: option.value }))}
                          >
                            <div className="text-center">
                              <div className="font-semibold">{option.label}</div>
                              <div className="text-sm text-gray-600">{option.desc}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-base font-semibold mb-3 block">Fitness Level</Label>
                      <Select
                        value={tourData.fitnessLevel}
                        onValueChange={(value) => setTourData(prev => ({ ...prev, fitnessLevel: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low - Minimal walking</SelectItem>
                          <SelectItem value="moderate">Moderate - Some walking</SelectItem>
                          <SelectItem value="high">High - Extensive walking/hiking</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-base font-semibold mb-3 block">What interests you most?</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {interests.map((interest) => (
                          <div key={interest} className="flex items-center space-x-2">
                            <Checkbox
                              checked={tourData.interests.includes(interest)}
                              onCheckedChange={(checked) => 
                                handleArrayToggle(tourData.interests, interest, 'interests')
                              }
                            />
                            <Label className="text-sm">{interest}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 4: Activities */}
                {step === 4 && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-base font-semibold mb-3 block">Preferred Activities</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {activities.map((activity) => (
                          <div key={activity} className="flex items-center space-x-2">
                            <Checkbox
                              checked={tourData.activities.includes(activity)}
                              onCheckedChange={(checked) => 
                                handleArrayToggle(tourData.activities, activity, 'activities')
                              }
                            />
                            <Label className="text-sm">{activity}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={tourData.photographyInterest}
                        onCheckedChange={(checked) => 
                          setTourData(prev => ({ ...prev, photographyInterest: checked as boolean }))
                        }
                      />
                      <Label>I'm particularly interested in wildlife photography</Label>
                    </div>

                    <div>
                      <Label htmlFor="special" className="text-base font-semibold">Special Requests or Requirements</Label>
                      <Textarea
                        id="special"
                        value={tourData.specialRequests}
                        onChange={(e) => setTourData(prev => ({ ...prev, specialRequests: e.target.value }))}
                        placeholder="Any dietary restrictions, mobility requirements, celebration occasions, or other special requests..."
                        className="mt-2"
                      />
                    </div>
                  </div>
                )}

                {/* Step 5: Contact */}
                {step === 5 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-semibold mb-2">Almost Done!</h3>
                      <p className="text-gray-600">
                        Provide your contact information and we'll create a personalized itinerary for you.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          id="name"
                          value={tourData.name}
                          onChange={(e) => setTourData(prev => ({ ...prev, name: e.target.value }))}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={tourData.email}
                          onChange={(e) => setTourData(prev => ({ ...prev, email: e.target.value }))}
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={tourData.phone}
                        onChange={(e) => setTourData(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>

                    {/* Summary */}
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h4 className="font-semibold mb-4">Your Custom Safari Summary:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>Duration:</strong> {tourData.duration} days<br />
                          <strong>Participants:</strong> {tourData.participants}<br />
                          <strong>Budget:</strong> ${tourData.budget[0].toLocaleString()}/person<br />
                          <strong>Accommodation:</strong> {tourData.accommodation}
                        </div>
                        <div>
                          <strong>Destinations:</strong> {tourData.destinations.length} selected<br />
                          <strong>Interests:</strong> {tourData.interests.length} selected<br />
                          <strong>Activities:</strong> {tourData.activities.length} selected<br />
                          <strong>Fitness Level:</strong> {tourData.fitnessLevel}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={step === 1}
                  >
                    Previous
                  </Button>
                  {step < 5 ? (
                    <Button onClick={nextStep} className="bg-orange-600 hover:bg-orange-700">
                      Next
                    </Button>
                  ) : (
                    <Button onClick={handleSubmit} className="bg-orange-600 hover:bg-orange-700">
                      Send My Request
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TourBuilder;
