
import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plane, 
  Shield, 
  MapPin, 
  Calendar, 
  Thermometer, 
  Camera, 
  Backpack, 
  Heart, 
  Globe, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Mountain,
  Binoculars
} from 'lucide-react';

const TravelResources = () => {
  const [selectedPackingList, setSelectedPackingList] = useState('safari');

  const visaRequirements = [
    {
      country: 'United States',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Flight itinerary']
    },
    {
      country: 'United Kingdom',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Hotel booking']
    },
    {
      country: 'European Union',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Travel insurance']
    },
    {
      country: 'Canada',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Return ticket']
    }
  ];

  const vaccinations = [
    {
      vaccine: 'Yellow Fever',
      required: true,
      timing: '10 days before travel',
      validity: 'Lifetime',
      description: 'Required for entry from certain countries'
    },
    {
      vaccine: 'Hepatitis A',
      required: true,
      timing: '2 weeks before travel',
      validity: '20+ years',
      description: 'Protection against contaminated food/water'
    },
    {
      vaccine: 'Typhoid',
      required: true,
      timing: '1-2 weeks before travel',
      validity: '3 years',
      description: 'Recommended for all travelers'
    },
    {
      vaccine: 'Malaria Prevention',
      required: true,
      timing: '1-2 weeks before travel',
      validity: 'During stay',
      description: 'Anti-malarial medication required'
    },
    {
      vaccine: 'Hepatitis B',
      required: false,
      timing: '1 month before travel',
      validity: 'Lifetime',
      description: 'For extended stays or medical procedures'
    },
    {
      vaccine: 'Meningitis',
      required: false,
      timing: '2 weeks before travel',
      validity: '3-5 years',
      description: 'Recommended for dry season travel'
    }
  ];

  const packingLists = {
    safari: {
      title: 'Safari Essentials',
      categories: [
        {
          name: 'Clothing',
          items: [
            'Neutral colored clothing (khaki, beige, olive)',
            'Long-sleeved shirts (sun protection)',
            'Lightweight pants',
            'Wide-brimmed hat',
            'Light jacket for early morning drives',
            'Comfortable walking shoes',
            'Sandals for camp',
            'Warm socks'
          ]
        },
        {
          name: 'Photography Equipment',
          items: [
            'Camera with telephoto lens (200mm+)',
            'Extra batteries and memory cards',
            'Lens cleaning kit',
            'Binoculars (8x42 recommended)',
            'Tripod or monopod',
            'Dust-proof camera bag'
          ]
        },
        {
          name: 'Personal Items',
          items: [
            'Sunscreen (SPF 30+)',
            'Insect repellent',
            'Personal medications',
            'Hand sanitizer',
            'Wet wipes',
            'Flashlight or headlamp',
            'Power bank',
            'Travel adapter'
          ]
        }
      ]
    },
    kilimanjaro: {
      title: 'Kilimanjaro Trekking',
      categories: [
        {
          name: 'Clothing Layers',
          items: [
            'Base layers (moisture-wicking)',
            'Insulating layers (fleece/down)',
            'Waterproof jacket and pants',
            'Trekking pants',
            'Warm hat and sun hat',
            'Waterproof gloves',
            'Hiking socks (wool/synthetic)',
            'Gaiters'
          ]
        },
        {
          name: 'Footwear',
          items: [
            'Waterproof hiking boots',
            'Camp shoes (lightweight)',
            'Crampons (if climbing in ice season)',
            'Trekking poles'
          ]
        },
        {
          name: 'Equipment',
          items: [
            'Sleeping bag (rated to -10°C)',
            'Sleeping pad',
            'Daypack (30-40L)',
            'Headlamp with extra batteries',
            'Water bottles/hydration system',
            'Water purification tablets'
          ]
        }
      ]
    },
    cultural: {
      title: 'Cultural Tours',
      categories: [
        {
          name: 'Appropriate Clothing',
          items: [
            'Modest clothing (covering shoulders/knees)',
            'Comfortable walking shoes',
            'Light scarf or shawl',
            'Sun protection',
            'Casual evening wear'
          ]
        },
        {
          name: 'Cultural Interaction',
          items: [
            'Small gifts for children (pens, notebooks)',
            'Translation app or phrasebook',
            'Respectful attitude',
            'Open mind and curiosity'
          ]
        }
      ]
    }
  };

  const weatherSeasons = [
    {
      season: 'Dry Season',
      months: 'June - October',
      temperature: '20-28°C (68-82°F)',
      rainfall: 'Very low',
      wildlife: 'Excellent wildlife viewing',
      advantages: [
        'Clear skies for photography',
        'Animals gather at water sources',
        'Less vegetation for better visibility',
        'Ideal road conditions'
      ],
      considerations: [
        'Peak season - higher prices',
        'More tourists',
        'Dusty conditions'
      ]
    },
    {
      season: 'Short Rains',
      months: 'November - December',
      temperature: '22-30°C (72-86°F)',
      rainfall: 'Moderate',
      wildlife: 'Good wildlife viewing',
      advantages: [
        'Green landscapes',
        'Fewer tourists',
        'Calving season begins',
        'Lower prices'
      ],
      considerations: [
        'Occasional afternoon showers',
        'Some roads may be muddy'
      ]
    },
    {
      season: 'Long Rains',
      months: 'March - May',
      temperature: '20-26°C (68-79°F)',
      rainfall: 'High',
      wildlife: 'Challenging wildlife viewing',
      advantages: [
        'Lowest prices',
        'Lush green scenery',
        'Great for photography',
        'Fewer crowds'
      ],
      considerations: [
        'Heavy rainfall',
        'Some camps may close',
        'Difficult road conditions',
        'Dense vegetation'
      ]
    }
  ];

  const culturalEtiquette = [
    {
      title: 'Greetings',
      description: 'Use both hands when greeting elders. "Jambo" (hello) is widely understood.',
      icon: Users
    },
    {
      title: 'Photography',
      description: 'Always ask permission before photographing people, especially in villages.',
      icon: Camera
    },
    {
      title: 'Dress Code',
      description: 'Dress modestly, especially when visiting villages or religious sites.',
      icon: Shield
    },
    {
      title: 'Gift Giving',
      description: 'Small educational gifts for children are appreciated, but avoid encouraging begging.',
      icon: Heart
    },
    {
      title: 'Respect',
      description: 'Show respect for local customs and traditions. Ask questions with genuine interest.',
      icon: CheckCircle
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16 md:pt-20">
        {/* Hero Section */}
        <div className="relative h-64 md:h-80 lg:h-96 overflow-hidden">
          <div 
            className="absolute inset-0 w-full h-full"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-3 md:mb-4 bg-orange-600 text-white px-3 md:px-4 py-1 md:py-2 text-xs md:text-sm">
                <Globe className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                Travel Resources
              </Badge>
              <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 md:mb-6">Plan Your Adventure</h1>
              <p className="text-base md:text-xl lg:text-2xl leading-relaxed opacity-90 max-w-3xl mx-auto">
                Everything you need to know for a safe and memorable Tanzania safari experience
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8 md:py-16">
          <Tabs defaultValue="visa" className="w-full">
            <div className="overflow-x-auto mb-6 md:mb-8">
              <TabsList className="grid w-full min-w-[500px] md:min-w-0 grid-cols-5 h-auto">
                <TabsTrigger value="visa" className="text-xs md:text-sm py-2 md:py-3 px-2 md:px-4">Visa & Entry</TabsTrigger>
                <TabsTrigger value="health" className="text-xs md:text-sm py-2 md:py-3 px-2 md:px-4">Health & Safety</TabsTrigger>
                <TabsTrigger value="packing" className="text-xs md:text-sm py-2 md:py-3 px-2 md:px-4">Packing Guide</TabsTrigger>
                <TabsTrigger value="weather" className="text-xs md:text-sm py-2 md:py-3 px-2 md:px-4">Weather Guide</TabsTrigger>
                <TabsTrigger value="culture" className="text-xs md:text-sm py-2 md:py-3 px-2 md:px-4">Cultural Tips</TabsTrigger>
              </TabsList>
            </div>

            {/* Visa Requirements */}
            <TabsContent value="visa" className="space-y-6 md:space-y-8">
              <div className="text-center mb-6 md:mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Visa & Entry Requirements</h2>
                <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
                  Most visitors to Tanzania require a visa. Here's what you need to know about entry requirements.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                {visaRequirements.map((visa, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3 md:pb-4">
                      <CardTitle className="flex items-center gap-2 text-base md:text-lg">
                        <Globe className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
                        {visa.country}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 md:space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 text-sm md:text-base">Requirement:</span>
                          <Badge variant="outline" className="text-orange-600 border-orange-600 text-xs md:text-sm">
                            {visa.requirement}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 text-sm md:text-base">Processing Time:</span>
                          <span className="font-medium text-sm md:text-base">{visa.processing}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 text-sm md:text-base">Validity:</span>
                          <span className="font-medium text-sm md:text-base">{visa.validity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 text-sm md:text-base">Cost:</span>
                          <span className="font-medium text-sm md:text-base">{visa.cost}</span>
                        </div>
                        <div>
                          <span className="text-gray-600 block mb-2 text-sm md:text-base">Required Documents:</span>
                          <ul className="text-xs md:text-sm space-y-1">
                            {visa.documents.map((doc, i) => (
                              <li key={i} className="flex items-center gap-2">
                                <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                                {doc}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 md:p-6">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 text-orange-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-orange-900 mb-2 text-sm md:text-base">Important Notes</h3>
                    <ul className="text-orange-800 space-y-1 text-xs md:text-sm">
                      <li>• Visa on arrival is available at major entry points</li>
                      <li>• Passport must be valid for at least 6 months</li>
                      <li>• Yellow fever certificate may be required</li>
                      <li>• Keep copies of all documents</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Health & Safety */}
            <TabsContent value="health" className="space-y-6 md:space-y-8">
              <div className="text-center mb-6 md:mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Health & Safety Information</h2>
                <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
                  Stay healthy and safe during your Tanzania adventure with proper preparation.
                </p>
              </div>

              <div>
                <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 flex items-center gap-2">
                  <Shield className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
                  Recommended Vaccinations
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                  {vaccinations.map((vaccine, index) => (
                    <Card key={index}>
                      <CardContent className="p-3 md:p-4">
                        <div className="flex items-start justify-between mb-2 md:mb-3">
                          <h4 className="font-semibold text-sm md:text-base">{vaccine.vaccine}</h4>
                          <Badge 
                            variant={vaccine.required ? "default" : "outline"}
                            className={`text-xs ${vaccine.required ? "bg-red-600" : "text-orange-600 border-orange-600"}`}
                          >
                            {vaccine.required ? "Required" : "Recommended"}
                          </Badge>
                        </div>
                        <div className="space-y-1 md:space-y-2 text-xs md:text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Timing:</span>
                            <span>{vaccine.timing}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Validity:</span>
                            <span>{vaccine.validity}</span>
                          </div>
                          <p className="text-gray-600 text-xs mt-2">{vaccine.description}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 md:p-6">
                <h3 className="font-semibold text-blue-900 mb-3 md:mb-4 flex items-center gap-2 text-sm md:text-base">
                  <Heart className="h-4 w-4 md:h-5 md:w-5" />
                  Health Tips
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 text-blue-800 text-xs md:text-sm">
                  <ul className="space-y-1 md:space-y-2">
                    <li>• Drink only bottled or boiled water</li>
                    <li>• Use insect repellent regularly</li>
                    <li>• Sleep under mosquito nets</li>
                    <li>• Wash hands frequently</li>
                  </ul>
                  <ul className="space-y-1 md:space-y-2">
                    <li>• Avoid raw or undercooked food</li>
                    <li>• Pack a first aid kit</li>
                    <li>• Consider travel insurance</li>
                    <li>• Consult a travel doctor</li>
                  </ul>
                </div>
              </div>
            </TabsContent>

            {/* Packing Guide */}
            <TabsContent value="packing" className="space-y-6 md:space-y-8">
              <div className="text-center mb-6 md:mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Packing Guides</h2>
                <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
                  Pack smart for your specific adventure with our comprehensive packing lists.
                </p>
              </div>

              <div className="flex justify-center mb-6 md:mb-8">
                <div className="flex flex-wrap gap-2 justify-center">
                  {Object.entries(packingLists).map(([key, list]) => (
                    <Button
                      key={key}
                      variant={selectedPackingList === key ? "default" : "outline"}
                      onClick={() => setSelectedPackingList(key)}
                      className={`text-xs md:text-sm px-3 md:px-4 py-2 md:py-3 ${selectedPackingList === key ? "bg-orange-600 hover:bg-orange-700" : ""}`}
                    >
                      {key === 'safari' && <Binoculars className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />}
                      {key === 'kilimanjaro' && <Mountain className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />}
                      {key === 'cultural' && <Users className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />}
                      <span className="hidden sm:inline">{list.title}</span>
                      <span className="sm:hidden">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 flex items-center gap-2">
                  <Backpack className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
                  {packingLists[selectedPackingList as keyof typeof packingLists].title}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  {packingLists[selectedPackingList as keyof typeof packingLists].categories.map((category, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-3 md:pb-4">
                        <CardTitle className="text-base md:text-lg">{category.name}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1 md:space-y-2">
                          {category.items.map((item, i) => (
                            <li key={i} className="flex items-start gap-2 text-xs md:text-sm">
                              <CheckCircle className="h-3 w-3 text-green-600 mt-1 flex-shrink-0" />
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Weather Guide */}
            <TabsContent value="weather" className="space-y-6 md:space-y-8">
              <div className="text-center mb-6 md:mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Weather & Best Times to Visit</h2>
                <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
                  Plan your visit according to Tanzania's seasonal patterns for the best experience.
                </p>
              </div>

              <div className="space-y-4 md:space-y-6">
                {weatherSeasons.map((season, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3 md:pb-4">
                      <CardTitle className="flex flex-col sm:flex-row sm:items-center gap-2 text-base md:text-lg">
                        <div className="flex items-center gap-2">
                          <Thermometer className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
                          {season.season}
                        </div>
                        <Badge variant="outline" className="self-start sm:ml-auto text-xs md:text-sm">
                          {season.months}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-4 md:mb-6">
                        <div>
                          <span className="text-gray-600 text-xs md:text-sm">Temperature</span>
                          <p className="font-medium text-sm md:text-base">{season.temperature}</p>
                        </div>
                        <div>
                          <span className="text-gray-600 text-xs md:text-sm">Rainfall</span>
                          <p className="font-medium text-sm md:text-base">{season.rainfall}</p>
                        </div>
                        <div className="sm:col-span-2 lg:col-span-2">
                          <span className="text-gray-600 text-xs md:text-sm">Wildlife</span>
                          <p className="font-medium text-sm md:text-base">{season.wildlife}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                        <div>
                          <h4 className="font-semibold text-green-700 mb-2 md:mb-3 text-sm md:text-base">Advantages</h4>
                          <ul className="space-y-1">
                            {season.advantages.map((advantage, i) => (
                              <li key={i} className="flex items-start gap-2 text-xs md:text-sm">
                                <CheckCircle className="h-3 w-3 text-green-600 mt-1 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-orange-700 mb-2 md:mb-3 text-sm md:text-base">Considerations</h4>
                          <ul className="space-y-1">
                            {season.considerations.map((consideration, i) => (
                              <li key={i} className="flex items-start gap-2 text-xs md:text-sm">
                                <AlertTriangle className="h-3 w-3 text-orange-600 mt-1 flex-shrink-0" />
                                {consideration}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Cultural Tips */}
            <TabsContent value="culture" className="space-y-6 md:space-y-8">
              <div className="text-center mb-6 md:mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Cultural Etiquette & Tips</h2>
                <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
                  Respect local customs and enhance your cultural experiences with these guidelines.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {culturalEtiquette.map((tip, index) => (
                  <Card key={index}>
                    <CardContent className="p-4 md:p-6 text-center">
                      <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4">
                        <tip.icon className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
                      </div>
                      <h3 className="font-semibold mb-2 md:mb-3 text-sm md:text-base">{tip.title}</h3>
                      <p className="text-gray-600 text-xs md:text-sm">{tip.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 md:p-6">
                <h3 className="font-semibold text-green-900 mb-3 md:mb-4 text-sm md:text-base">Additional Cultural Tips</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 text-green-800 text-xs md:text-sm">
                  <ul className="space-y-1 md:space-y-2">
                    <li>• Learn basic Swahili phrases</li>
                    <li>• Remove shoes when entering homes</li>
                    <li>• Use your right hand for greetings</li>
                    <li>• Be patient with different time concepts</li>
                  </ul>
                  <ul className="space-y-1 md:space-y-2">
                    <li>• Bargaining is expected in markets</li>
                    <li>• Avoid pointing with your finger</li>
                    <li>• Respect religious practices</li>
                    <li>• Support local communities responsibly</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-orange-600 to-orange-700 py-12 md:py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-3 md:mb-4">Need Personal Travel Assistance?</h2>
            <p className="text-lg md:text-xl text-orange-100 mb-6 md:mb-8 max-w-2xl mx-auto">
              Our travel experts are here to help you plan every detail of your Tanzania adventure
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
              <Button className="bg-white text-orange-600 hover:bg-gray-100 font-semibold px-6 md:px-8 py-2 md:py-3 text-sm md:text-base">
                Contact Travel Expert
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 px-6 md:px-8 py-2 md:py-3 text-sm md:text-base">
                Download PDF Guide
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TravelResources;
