
import React, { useState, useMemo } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Camera, Loader2 } from 'lucide-react';
import { useGallery } from '@/hooks/useGallery';

const Gallery = () => {
  const { images, loading, error } = useGallery();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Filter images based on search and category
  const filteredImages = useMemo(() => images.filter(image => {
      const matchesSearch = image.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.location.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = !selectedCategory || image.category === selectedCategory;
      return matchesSearch && matchesCategory;
    }), [images, searchTerm, selectedCategory]);

  // Get unique categories
  const categories = useMemo(() => Array.from(new Set(images.map(image => image.category))), [images]);

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
            <div className="container mx-auto px-4 text-center">
              <h1 className="text-4xl md:text-5xl font-bubblegum font-bold mb-4">Safari Gallery</h1>
              <p className="text-xl opacity-90 max-w-2xl mx-auto">
                Breathtaking moments captured from our safari adventures
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-16 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading gallery images...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
            <div className="container mx-auto px-4 text-center">
              <h1 className="text-4xl md:text-5xl font-bubblegum font-bold mb-4">Safari Gallery</h1>
              <p className="text-xl opacity-90 max-w-2xl mx-auto">
                Breathtaking moments captured from our safari adventures
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-16 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bubblegum font-bold mb-4">Safari Gallery</h1>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              Breathtaking moments captured from our safari adventures
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-12">
          {/* Search and Filter */}
          <div className="mb-8 flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant={selectedCategory === '' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('')}
                size="sm"
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category)}
                  size="sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Gallery Grid */}
          {filteredImages.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg mb-4">
                {images.length === 0 ? 'No images available yet.' : 'No images match your search criteria.'}
              </p>
              {searchTerm && (
                <Button onClick={() => setSearchTerm('')}>
                  Clear Search
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredImages.map((image) => (
                <div
                  key={image.id}
                  className="group relative aspect-square overflow-hidden rounded-lg cursor-pointer bg-gray-100"
                  onClick={() => setSelectedImage(image.url)}
                >
                  <img
                    src={image.url.includes('http') 
                      ? image.url 
                      : `https://images.unsplash.com/${image.url}?auto=format&fit=crop&w=400&h=400`
                    }
                    alt={image.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                    <h3 className="text-white font-semibold text-sm mb-1">{image.title}</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-white text-xs">
                        <MapPin className="h-3 w-3 mr-1" />
                        {image.location}
                      </div>
                      <div className="flex items-center text-white text-xs">
                        <Camera className="h-3 w-3 mr-1" />
                        {image.photographer}
                      </div>
                    </div>
                    <Badge className="mt-2 w-fit">{image.category}</Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Modal for full-size image */}
        {selectedImage && (
          <div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <img
                src={selectedImage.includes('http') 
                  ? selectedImage 
                  : `https://images.unsplash.com/${selectedImage}?auto=format&fit=crop&w=1200&h=800`
                }
                alt="Full size"
                className="max-w-full max-h-full object-contain"
              />
              <button
                className="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70"
                onClick={() => setSelectedImage(null)}
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
};

export default Gallery;
