
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface Filters {
  category: string;
  accommodationLevel: string;
  priceRange: number[];
  duration: string;
  destinations: string[];
}

interface TourFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
}

const TourFilters: React.FC<TourFiltersProps> = ({ filters, onFiltersChange }) => {
  const categories = ['Wildlife Safari', 'Cultural', 'Adventure', 'Photography', 'Luxury'];
  const accommodationLevels = ['Budget', 'Mid-range', 'Luxury'];
  const destinations = ['Serengeti', 'Ngorongoro', 'Tarangire', 'Lake Manyara', 'Mount Kilimanjaro', 'Maasai Villages'];

  const handleFilterChange = (key: keyof Filters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const clearFilters = () => {
    onFiltersChange({
      category: 'all',
      accommodationLevel: 'all',
      priceRange: [0, 10000],
      duration: 'all',
      destinations: []
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filter Tours</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Category Filter */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Tour Category</Label>
          <Select value={filters.category} onValueChange={(value) => handleFilterChange('category', value)}>
            <SelectTrigger>
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Accommodation Level */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Accommodation Level</Label>
          <Select value={filters.accommodationLevel} onValueChange={(value) => handleFilterChange('accommodationLevel', value)}>
            <SelectTrigger>
              <SelectValue placeholder="All Levels" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              {accommodationLevels.map((level) => (
                <SelectItem key={level} value={level}>{level}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label className="text-sm font-medium mb-2 block">
            Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
          </Label>
          <Slider
            value={filters.priceRange}
            onValueChange={(value) => handleFilterChange('priceRange', value)}
            max={10000}
            min={0}
            step={100}
            className="w-full"
          />
        </div>

        {/* Duration */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Duration</Label>
          <Select value={filters.duration} onValueChange={(value) => handleFilterChange('duration', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Any Duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Any Duration</SelectItem>
              <SelectItem value="1-3 days">1-3 days</SelectItem>
              <SelectItem value="4-7 days">4-7 days</SelectItem>
              <SelectItem value="8+ days">8+ days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Destinations */}
        <div>
          <Label className="text-sm font-medium mb-3 block">Destinations</Label>
          <div className="space-y-2">
            {destinations.map((destination) => (
              <div key={destination} className="flex items-center space-x-2">
                <Checkbox
                  id={destination}
                  checked={filters.destinations.includes(destination)}
                  onCheckedChange={(checked) => {
                    const newDestinations = checked
                      ? [...filters.destinations, destination]
                      : filters.destinations.filter(d => d !== destination);
                    handleFilterChange('destinations', newDestinations);
                  }}
                />
                <Label htmlFor={destination} className="text-sm">{destination}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Clear Filters */}
        <Button onClick={clearFilters} variant="outline" className="w-full">
          Clear All Filters
        </Button>
      </CardContent>
    </Card>
  );
};

export default TourFilters;
