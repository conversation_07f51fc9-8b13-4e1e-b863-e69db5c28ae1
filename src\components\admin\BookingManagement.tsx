
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calendar, Users, DollarSign, MapPin, Phone, Mail, Search, Filter, Eye, Edit, Check, X } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { useToast } from '@/hooks/use-toast';

const BookingManagement = () => {
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      setLoading(true);
      const data = await FirebaseService.getBookings();
      setBookings(data || []);
    } catch (error) {
      console.error('Error loading bookings:', error);
      toast({
        title: "Error loading bookings",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateBookingStatus = async (bookingId: string, newStatus: string) => {
    try {
      const validStatus = newStatus as 'pending' | 'confirmed' | 'cancelled' | 'completed';
      await FirebaseService.updateBooking(bookingId, { status: validStatus });
      await loadBookings();
      toast({
        title: "Booking status updated successfully"
      });
    } catch (error) {
      toast({
        title: "Error updating booking status",
        variant: "destructive"
      });
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = 
      booking.customerInfo?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.customerInfo?.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.customerInfo?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.tourTitle?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const BookingDetailsModal = ({ booking, onClose }: { booking: any; onClose: () => void }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Booking Details</h2>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <strong>Name:</strong> {booking.customerInfo?.firstName} {booking.customerInfo?.lastName}
              </div>
              <div>
                <strong>Email:</strong> {booking.customerInfo?.email}
              </div>
              <div>
                <strong>Phone:</strong> {booking.customerInfo?.phone}
              </div>
              <div>
                <strong>Country:</strong> {booking.customerInfo?.country}
              </div>
              {booking.customerInfo?.passportNumber && (
                <div>
                  <strong>Passport:</strong> {booking.customerInfo.passportNumber}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tour Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                Tour Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <strong>Tour:</strong> {booking.tourTitle}
              </div>
              <div>
                <strong>Start Date:</strong> {booking.bookingDetails?.startDate}
              </div>
              <div>
                <strong>End Date:</strong> {booking.bookingDetails?.endDate}
              </div>
              <div>
                <strong>Participants:</strong> {booking.bookingDetails?.participants}
              </div>
              <div>
                <strong>Accommodation:</strong> {booking.bookingDetails?.accommodationType}
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <strong>Total Amount:</strong> ${booking.pricing?.totalAmount?.toLocaleString()}
              </div>
              <div>
                <strong>Payment Status:</strong> 
                <Badge className={`ml-2 ${getStatusColor(booking.paymentInfo?.paymentStatus)}`}>
                  {booking.paymentInfo?.paymentStatus}
                </Badge>
              </div>
              <div>
                <strong>Payment Method:</strong> {booking.paymentInfo?.paymentMethod}
              </div>
              {booking.paymentInfo?.transactionId && (
                <div>
                  <strong>Transaction ID:</strong> {booking.paymentInfo.transactionId}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Emergency Contact */}
          {booking.customerInfo?.emergencyContact && (
            <Card>
              <CardHeader>
                <CardTitle>Emergency Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <strong>Name:</strong> {booking.customerInfo.emergencyContact.name}
                </div>
                <div>
                  <strong>Phone:</strong> {booking.customerInfo.emergencyContact.phone}
                </div>
                <div>
                  <strong>Relationship:</strong> {booking.customerInfo.emergencyContact.relationship}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Special Requests */}
        {booking.bookingDetails?.specialRequests && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Special Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{booking.bookingDetails.specialRequests}</p>
            </CardContent>
          </Card>
        )}

        {/* Status Update */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t">
          <div className="flex items-center space-x-4">
            <span className="font-semibold">Update Status:</span>
            <Select
              value={booking.status}
              onValueChange={(value) => updateBookingStatus(booking.id, value)}
            >
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold">Booking Management</h2>
        <Button onClick={loadBookings}>
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Search by customer name, email, or tour..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <CardContent className="pt-6">
          {loading ? (
            <div className="text-center py-8">Loading bookings...</div>
          ) : filteredBookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No bookings found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Booking ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Tour</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Participants</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBookings.map((booking) => (
                  <TableRow key={booking.id}>
                    <TableCell className="font-mono text-sm">
                      {booking.id?.slice(-8) || 'N/A'}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {booking.customerInfo?.firstName} {booking.customerInfo?.lastName}
                        </div>
                        <div className="text-sm text-gray-600">
                          {booking.customerInfo?.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{booking.tourTitle || 'N/A'}</TableCell>
                    <TableCell>{booking.bookingDetails?.startDate || 'N/A'}</TableCell>
                    <TableCell>{booking.bookingDetails?.participants || 0}</TableCell>
                    <TableCell>${booking.pricing?.totalAmount?.toLocaleString() || '0'}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(booking.status)}>
                        {booking.status || 'pending'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedBooking(booking)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => updateBookingStatus(booking.id, 'confirmed')}
                        >
                          <Check className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Booking Details Modal */}
      {selectedBooking && (
        <BookingDetailsModal
          booking={selectedBooking}
          onClose={() => setSelectedBooking(null)}
        />
      )}
    </div>
  );
};

export default BookingManagement;
