
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration - replace with your actual config
const firebaseConfig = {
  apiKey: "AIzaSyDOjtsTduE4ZeBD6lguLyMRdxUsFXFcFkE",
  authDomain: "wildlife-dda6c.firebaseapp.com",
  projectId: "wildlife-dda6c",
  storageBucket: "wildlife-dda6c.firebasestorage.app",
  messagingSenderId: "594462561158",
  appId: "1:594462561158:web:feaf36598ab0a12bfc1207",
  measurementId: "G-QB5PQNKDG9"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
