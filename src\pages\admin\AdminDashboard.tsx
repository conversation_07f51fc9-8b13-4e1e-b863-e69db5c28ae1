
import React from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { Routes, Route } from 'react-router-dom';
import AdminOverview from './AdminOverview';
import AdminReviews from './AdminReviews';
import AdminBlog from './AdminBlog';
import AdminGallery from './AdminGallery';
import TourManagement from '@/components/admin/TourManagement';
import BookingManagement from '@/components/admin/BookingManagement';
import UserManagement from '@/components/admin/UserManagement';
import MessageManagement from '@/components/admin/MessageManagement';
import DestinationManagement from '@/components/admin/DestinationManagement';
import CustomTours from './CustomTours';

const AdminDashboard = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AdminSidebar />
        <SidebarInset>
          <main className="flex-1 p-3 md:p-6 overflow-x-auto">
            <Routes>
              <Route index element={<AdminOverview />} />
              <Route path="tours" element={<TourManagement />} />
              <Route path="bookings" element={<BookingManagement />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="reviews" element={<AdminReviews />} />
              <Route path="messages" element={<MessageManagement />} />
              <Route path="blog" element={<AdminBlog />} />
              <Route path="gallery" element={<AdminGallery />} />
              <Route path="destinations" element={<DestinationManagement />} />
              <Route path="custom-tours" element={<CustomTours />} />
            </Routes>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default AdminDashboard;
