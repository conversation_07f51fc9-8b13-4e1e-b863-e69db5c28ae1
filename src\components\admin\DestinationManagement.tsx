
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';

interface Destination {
  id?: string;
  name: string;
  description: string;
  country: string;
  region: string;
  coordinates: { lat: number; lng: number };
  bestTimeToVisit: string[];
  climate: string;
  wildlife: Array<{
    species: string;
    scientificName: string;
    category: string;
    abundance: string;
    bestSpottingTime: string;
    behavior: string;
    conservationStatus: string;
    photographyTips: string;
  }>;
  images: string[];
  activities: string[];
  accommodations: string[];
  featured: boolean;
  detailedGuide: {
    overview: string;
    geography: string;
    history: string;
    gettingThere: string;
    accommodation: string;
    packingTips: string[];
    healthSafety: string;
    travelTips: string[];
  };
  conservationInfo: {
    initiatives: string[];
    challenges: string[];
    howTouristsHelp: string[];
    conservationFee: number;
  };
  culturalInfo: {
    tribes: string[];
    languages: string[];
    traditions: string[];
    etiquette: string[];
    culturalSites: string[];
  };
}

const DestinationManagement = () => {
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingDestination, setEditingDestination] = useState<Destination | null>(null);
  const [showForm, setShowForm] = useState(false);
  const { toast } = useToast();

  const initialDestinationState: Destination = {
    name: '',
    description: '',
    country: '',
    region: '',
    coordinates: { lat: 0, lng: 0 },
    bestTimeToVisit: [],
    climate: '',
    wildlife: [],
    images: [],
    activities: [],
    accommodations: [],
    featured: false,
    detailedGuide: {
      overview: '',
      geography: '',
      history: '',
      gettingThere: '',
      accommodation: '',
      packingTips: [],
      healthSafety: '',
      travelTips: []
    },
    conservationInfo: {
      initiatives: [],
      challenges: [],
      howTouristsHelp: [],
      conservationFee: 0
    },
    culturalInfo: {
      tribes: [],
      languages: [],
      traditions: [],
      etiquette: [],
      culturalSites: []
    }
  };

  const [formData, setFormData] = useState<Destination>(initialDestinationState);

  useEffect(() => {
    fetchDestinations();
  }, []);

  const fetchDestinations = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      const destinationsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Destination[];
      setDestinations(destinationsData);
    } catch (error) {
      console.error('Error fetching destinations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch destinations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const destinationData = {
        ...formData,
        updatedAt: Timestamp.now(),
        ...(editingDestination ? {} : { createdAt: Timestamp.now() })
      };

      if (editingDestination && editingDestination.id) {
        await updateDoc(doc(db, 'destinations', editingDestination.id), destinationData);
        toast({
          title: "Success",
          description: "Destination updated successfully",
        });
      } else {
        await addDoc(collection(db, 'destinations'), destinationData);
        toast({
          title: "Success",
          description: "Destination created successfully",
        });
      }

      setShowForm(false);
      setEditingDestination(null);
      setFormData(initialDestinationState);
      fetchDestinations();
    } catch (error) {
      console.error('Error saving destination:', error);
      toast({
        title: "Error",
        description: "Failed to save destination",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (destinationId: string) => {
    if (!confirm('Are you sure you want to delete this destination?')) return;
    
    setLoading(true);
    try {
      await deleteDoc(doc(db, 'destinations', destinationId));
      toast({
        title: "Success",
        description: "Destination deleted successfully",
      });
      fetchDestinations();
    } catch (error) {
      console.error('Error deleting destination:', error);
      toast({
        title: "Error",
        description: "Failed to delete destination",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (destination: Destination) => {
    setEditingDestination(destination);
    setFormData(destination);
    setShowForm(true);
  };

  const handleArrayInput = (field: string, value: string, nestedField?: string) => {
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item);
    
    if (nestedField) {
      setFormData(prev => ({
        ...prev,
        [field]: {
          ...prev[field as keyof Destination] as any,
          [nestedField]: arrayValue
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: arrayValue }));
    }
  };

  if (showForm) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>{editingDestination ? 'Edit Destination' : 'Create New Destination'}</CardTitle>
          <CardDescription>
            Fill out all the details for the safari destination
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="wildlife">Wildlife</TabsTrigger>
              <TabsTrigger value="guide">Travel Guide</TabsTrigger>
              <TabsTrigger value="conservation">Conservation</TabsTrigger>
              <TabsTrigger value="cultural">Cultural</TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Destination Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Serengeti National Park"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                    placeholder="Tanzania"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="World-famous for the Great Migration and abundant wildlife"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="region">Region</Label>
                  <Input
                    id="region"
                    value={formData.region}
                    onChange={(e) => setFormData(prev => ({ ...prev, region: e.target.value }))}
                    placeholder="Northern Tanzania"
                  />
                </div>
                <div>
                  <Label htmlFor="climate">Climate</Label>
                  <Input
                    id="climate"
                    value={formData.climate}
                    onChange={(e) => setFormData(prev => ({ ...prev, climate: e.target.value }))}
                    placeholder="Tropical savanna climate"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="lat">Latitude</Label>
                  <Input
                    id="lat"
                    type="number"
                    step="any"
                    value={formData.coordinates.lat}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      coordinates: { ...prev.coordinates, lat: Number(e.target.value) }
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="lng">Longitude</Label>
                  <Input
                    id="lng"
                    type="number"
                    step="any"
                    value={formData.coordinates.lng}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      coordinates: { ...prev.coordinates, lng: Number(e.target.value) }
                    }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bestTimeToVisit">Best Time to Visit (comma-separated months)</Label>
                <Input
                  id="bestTimeToVisit"
                  value={formData.bestTimeToVisit.join(', ')}
                  onChange={(e) => handleArrayInput('bestTimeToVisit', e.target.value)}
                  placeholder="June, July, August, September, October"
                />
              </div>

              <div>
                <Label htmlFor="activities">Activities (comma-separated)</Label>
                <Input
                  id="activities"
                  value={formData.activities.join(', ')}
                  onChange={(e) => handleArrayInput('activities', e.target.value)}
                  placeholder="Game Drives, Hot Air Balloon Safari, Walking Safari"
                />
              </div>

              <div>
                <Label htmlFor="accommodations">Accommodations (comma-separated)</Label>
                <Input
                  id="accommodations"
                  value={formData.accommodations.join(', ')}
                  onChange={(e) => handleArrayInput('accommodations', e.target.value)}
                  placeholder="Luxury Lodge, Tented Camp, Mobile Camp"
                />
              </div>

              <div>
                <Label htmlFor="images">Image URLs (comma-separated)</Label>
                <Textarea
                  id="images"
                  value={formData.images.join(', ')}
                  onChange={(e) => handleArrayInput('images', e.target.value)}
                  placeholder="https://images.unsplash.com/photo-1516426122078-c23e76319801"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                />
                <Label>Featured Destination</Label>
              </div>
            </TabsContent>

            <TabsContent value="guide" className="space-y-4">
              <div>
                <Label htmlFor="overview">Overview</Label>
                <Textarea
                  id="overview"
                  value={formData.detailedGuide.overview}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, overview: e.target.value }
                  }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="geography">Geography</Label>
                  <Textarea
                    id="geography"
                    value={formData.detailedGuide.geography}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      detailedGuide: { ...prev.detailedGuide, geography: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="history">History</Label>
                  <Textarea
                    id="history"
                    value={formData.detailedGuide.history}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      detailedGuide: { ...prev.detailedGuide, history: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="gettingThere">Getting There</Label>
                <Textarea
                  id="gettingThere"
                  value={formData.detailedGuide.gettingThere}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, gettingThere: e.target.value }
                  }))}
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="packingTips">Packing Tips (comma-separated)</Label>
                <Input
                  id="packingTips"
                  value={formData.detailedGuide.packingTips.join(', ')}
                  onChange={(e) => handleArrayInput('detailedGuide', e.target.value, 'packingTips')}
                  placeholder="Neutral colored clothing, Good camera, Binoculars"
                />
              </div>

              <div>
                <Label htmlFor="healthSafety">Health & Safety</Label>
                <Textarea
                  id="healthSafety"
                  value={formData.detailedGuide.healthSafety}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, healthSafety: e.target.value }
                  }))}
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="travelTips">Travel Tips (comma-separated)</Label>
                <Input
                  id="travelTips"
                  value={formData.detailedGuide.travelTips.join(', ')}
                  onChange={(e) => handleArrayInput('detailedGuide', e.target.value, 'travelTips')}
                  placeholder="Book in advance, Respect wildlife, Follow guide instructions"
                />
              </div>
            </TabsContent>

            <TabsContent value="conservation" className="space-y-4">
              <div>
                <Label htmlFor="conservationFee">Conservation Fee ($)</Label>
                <Input
                  id="conservationFee"
                  type="number"
                  value={formData.conservationInfo.conservationFee}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    conservationInfo: { ...prev.conservationInfo, conservationFee: Number(e.target.value) }
                  }))}
                />
              </div>

              <div>
                <Label htmlFor="initiatives">Conservation Initiatives (comma-separated)</Label>
                <Input
                  id="initiatives"
                  value={formData.conservationInfo.initiatives.join(', ')}
                  onChange={(e) => handleArrayInput('conservationInfo', e.target.value, 'initiatives')}
                  placeholder="Anti-poaching programs, Community conservation"
                />
              </div>

              <div>
                <Label htmlFor="challenges">Conservation Challenges (comma-separated)</Label>
                <Input
                  id="challenges"
                  value={formData.conservationInfo.challenges.join(', ')}
                  onChange={(e) => handleArrayInput('conservationInfo', e.target.value, 'challenges')}
                  placeholder="Human-wildlife conflict, Climate change"
                />
              </div>

              <div>
                <Label htmlFor="howTouristsHelp">How Tourists Help (comma-separated)</Label>
                <Input
                  id="howTouristsHelp"
                  value={formData.conservationInfo.howTouristsHelp.join(', ')}
                  onChange={(e) => handleArrayInput('conservationInfo', e.target.value, 'howTouristsHelp')}
                  placeholder="Park fees fund conservation, Employment for locals"
                />
              </div>
            </TabsContent>

            <TabsContent value="cultural" className="space-y-4">
              <div>
                <Label htmlFor="tribes">Local Tribes (comma-separated)</Label>
                <Input
                  id="tribes"
                  value={formData.culturalInfo.tribes.join(', ')}
                  onChange={(e) => handleArrayInput('culturalInfo', e.target.value, 'tribes')}
                  placeholder="Maasai"
                />
              </div>

              <div>
                <Label htmlFor="languages">Languages (comma-separated)</Label>
                <Input
                  id="languages"
                  value={formData.culturalInfo.languages.join(', ')}
                  onChange={(e) => handleArrayInput('culturalInfo', e.target.value, 'languages')}
                  placeholder="Swahili, English, Maasai"
                />
              </div>

              <div>
                <Label htmlFor="traditions">Traditions (comma-separated)</Label>
                <Input
                  id="traditions"
                  value={formData.culturalInfo.traditions.join(', ')}
                  onChange={(e) => handleArrayInput('culturalInfo', e.target.value, 'traditions')}
                  placeholder="Traditional dances, Beadwork, Cattle herding"
                />
              </div>

              <div>
                <Label htmlFor="etiquette">Cultural Etiquette (comma-separated)</Label>
                <Input
                  id="etiquette"
                  value={formData.culturalInfo.etiquette.join(', ')}
                  onChange={(e) => handleArrayInput('culturalInfo', e.target.value, 'etiquette')}
                  placeholder="Respect local customs, Ask before photographing people"
                />
              </div>

              <div>
                <Label htmlFor="culturalSites">Cultural Sites (comma-separated)</Label>
                <Input
                  id="culturalSites"
                  value={formData.culturalInfo.culturalSites.join(', ')}
                  onChange={(e) => handleArrayInput('culturalInfo', e.target.value, 'culturalSites')}
                  placeholder="Maasai villages, Rock paintings"
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowForm(false);
                setEditingDestination(null);
                setFormData(initialDestinationState);
              }}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Saving...' : 'Save Destination'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Destination Management</CardTitle>
            <CardDescription>Manage safari destinations and parks</CardDescription>
          </div>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add New Destination
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Loading destinations...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Country</TableHead>
                <TableHead>Region</TableHead>
                <TableHead>Conservation Fee</TableHead>
                <TableHead>Featured</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {destinations.map((destination) => (
                <TableRow key={destination.id}>
                  <TableCell className="font-medium">{destination.name}</TableCell>
                  <TableCell>{destination.country}</TableCell>
                  <TableCell>{destination.region}</TableCell>
                  <TableCell>${destination.conservationInfo.conservationFee}</TableCell>
                  <TableCell>
                    {destination.featured && <Badge variant="outline">Featured</Badge>}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(destination)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => destination.id && handleDelete(destination.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default DestinationManagement;
