
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Leaf, Droplets, Zap, TreePine } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const SustainabilityDashboard = () => {
  const { t } = useLanguage();

  const sustainabilityMetrics = [
    {
      icon: <Leaf className="h-5 w-5 text-green-600" />,
      title: 'Carbon Footprint',
      value: '2.3 tons CO₂',
      offset: '100%',
      progress: 100,
      description: 'Fully offset through tree planting'
    },
    {
      icon: <Droplets className="h-5 w-5 text-blue-600" />,
      title: 'Water Conservation',
      value: '1,200L saved',
      offset: '85%',
      progress: 85,
      description: 'Through efficient lodge practices'
    },
    {
      icon: <Zap className="h-5 w-5 text-yellow-600" />,
      title: 'Renewable Energy',
      value: '78% solar',
      offset: '78%',
      progress: 78,
      description: 'Solar-powered accommodations'
    },
    {
      icon: <TreePine className="h-5 w-5 text-green-700" />,
      title: 'Local Community',
      value: '$450 contributed',
      offset: '92%',
      progress: 92,
      description: 'Direct economic impact'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Leaf className="h-5 w-5 mr-2 text-green-600" />
          {t('sustainability.impact')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {sustainabilityMetrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {metric.icon}
                  <span className="font-medium">{metric.title}</span>
                </div>
                <Badge variant="secondary">{metric.value}</Badge>
              </div>
              <Progress value={metric.progress} className="h-2" />
              <p className="text-sm text-gray-600">{metric.description}</p>
            </div>
          ))}
        </div>
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Leaf className="h-4 w-4 text-green-600" />
            <span className="font-semibold text-green-800">Eco-Certified Tour</span>
          </div>
          <p className="text-sm text-green-700">
            This tour meets our highest sustainability standards and contributes to local conservation efforts.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SustainabilityDashboard;
